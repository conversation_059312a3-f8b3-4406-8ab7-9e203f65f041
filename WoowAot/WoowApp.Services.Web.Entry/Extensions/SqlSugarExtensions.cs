using Microsoft.Data.Sqlite;
using SqlSugar;
using WoowApp.Services.Core.DatabaseAccessor;
using WoowApp.Services.Core.Options;
using WoowApp.Services.Shared.Base;

namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// SqlSugar 扩展
/// </summary>
public static partial class SqlSugarExtensions
{
    /// <summary>
    /// add SqlSugar
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddSqlSugar(this IServiceCollection services)
    {
        SqlSugar.StaticConfig.EnableAot = true;
        AotHelper();
        var getDefaultOptions = () =>
        {
            var isExitDb = File.Exists(".\\main_data.db");
            return new DbConnectionOptions
            {
                ConnectionConfigs = new List<ConnectionConfig> {
                        new ConnectionConfig() {
                            DbType = SqlSugar.DbType.Sqlite,
                            ConfigId =WoowApp.Services.Application.Consts.AppConst.ConfigId, // "main",
                            ConnectionString =new SqliteConnectionStringBuilder(){ DataSource=".\\main_data.db", Mode=SqliteOpenMode.ReadWriteCreate, Password="08E03E499BB7476E86D3A905DE8E827D" }.ToString(),  //"DataSource=.\\main.db",
                            IsAutoCloseConnection = false,
                        }
                    },
                EnableAutoCreateDb = !isExitDb,
                EnableInitTable = !isExitDb,
            };
        };
        var dbOptions = Woow.GetOptions<DbConnectionOptions>();
        if (dbOptions == null || dbOptions.ConnectionConfigs == null)
        {
            dbOptions = getDefaultOptions();
            var sqlSugar = SqlSugarSetup.AddSqlSugarSetup(dbOptions);
            services.AddSingleton<ISqlSugarClient>(sqlSugar); // 单例注册
        }
        else
        {
            if (dbOptions.EnableAutoCreateDb || dbOptions.EnableInitTable)
            {
                var sqlSugar = SqlSugarSetup.AddSqlSugarSetup(dbOptions);
                services.AddSingleton<ISqlSugarClient>(sqlSugar);
            }
            else
            {
                services.AddSingleton<ISqlSugarClient>(service => SqlSugarSetup.AddSqlSugarSetup(dbOptions));
            }
        }
        services.AddScoped(typeof(SqlSugarRepositoryProxy<>));
        services.AddScoped(typeof(SqlSugarRepository<>)); // 注册仓储

        // load type
        SqliteQueryable<long> _ = new();

        return services;
    }

    static partial void AotHelper();

}
