using System.Diagnostics;
using WoowApp.Base;
using WoowApp.Services.Core.DatabaseAccessor;
using WoowApp.Services.Core.Enums;
using WoowApp.Services.Core.Filters;
using WoowApp.Services.Shared.Base;
using WoowApp.Services.Shared.JsonSerialize;

namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// Woow服务扩展
/// </summary>
public static class WoowServiceExtensions
{
    /// <summary>
    /// 设置Woow服务与事务Ioc
    /// </summary>
    /// <param name="app"></param>
    public static void UseWoowService(this IApplicationBuilder app)
    {
        // 同步Json设置
        app.ApplicationServices.GetService<JsonSerializerProvider>()?.Options.AddWoowJsonSerializerOptions();

        app.Use(async (context, next) =>
        {
            // 设置ServiceFactory
            Woow.SetServiceFactory(context.RequestServices.GetService<IServiceFactory>()!);
            // 启用事务特性
            UnitOfWorkAttribute.SetServiceProvider(context.RequestServices);

            var logOperateHandler = context.RequestServices.GetService<LogOperateHandler>()!;
            await logOperateHandler.OnActionExecutionAsync(context, next);

        });
    }
}
