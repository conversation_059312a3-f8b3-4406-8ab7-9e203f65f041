using System.Text.Json;
using System.Text.Json.Serialization;
using WoowApp.Services.Shared.JsonSerialize;
using WoowApp.Services.Shared.Snowflake;
using WoowApp.Services.Web.Entry.Serializers;

namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// JSON序列化扩展
/// </summary>
public static class SerializerExtensions
{
    public static IServiceCollection AddWoowSerializerOptions(this IServiceCollection services)
    {
        services.ConfigureHttpJsonOptions(options =>
        {
            options.SerializerOptions.AddWoowJsonSerializerOptions();
        });

        return services;
    }
    public static JsonSerializerOptions AddWoowJsonSerializerOptions(this JsonSerializerOptions serializerOptions)
    {
        serializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        serializerOptions.Converters.Add(new DateTimeConverter());
        serializerOptions.Converters.Add(new SnowflakeIdConverter());
        serializerOptions.Converters.Add(new SnowflakeIdsConverter());
        serializerOptions.Converters.Add(new SnowflakeIdNullEnableConverter());
        serializerOptions.Converters.Add(new SnowflakeIdsNullEnableConverter());
        //serializerOptions.Converters.Add(new JsonStringEnumConverter());
        //serializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
        //serializerOptions.TypeInfoResolverChain.Insert(0, GeneratorServiceDtoJsonSerializerContext.Default);
        serializerOptions.AddWoowDtoSerializerOptions();
        serializerOptions.TypeInfoResolverChain.Insert(0, GeneratorJsonSerializerContext.Default);
        serializerOptions.TypeInfoResolverChain.Insert(0, AppJsonSerializerContext.Default);
        return serializerOptions;
    }
}
