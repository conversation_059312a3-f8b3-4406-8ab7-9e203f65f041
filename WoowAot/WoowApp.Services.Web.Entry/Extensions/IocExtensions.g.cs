// <auto-generated/>
using WoowApp.Services.Core.Service;
using WoowApp.Services.Core.Utils;
namespace WoowApp.Services.Web.Entry.Extensions;

public static partial class IocExtensions
{
    static partial void AddServices(this IServiceCollection services)
    {
                services.AddTransient<global::WoowApp.Services.Core.ApplicationConfigCategoryService>();
        services.AddScoped<global::WoowApp.Services.Core.Managers.ApplicationConfigManager>();
        services.AddTransient<global::WoowApp.Services.Core.ApplicationConfigService>();
        services.AddTransient<global::WoowApp.Services.Core.ApplicationPrintTemplateManageService>();
        services.AddTransient<global::WoowApp.Services.Core.ApplicationUserConfigManageService>();
        services.AddTransient<global::WoowApp.Services.Core.Services.AppLockFactory>();
        services.AddTransient<global::WoowApp.Services.Core.Service.AuthService>();
        services.AddSingleton<global::WoowApp.Services.Application.Models.ClientContainer>();
        services.AddScoped<global::WoowApp.Services.Core.Service.CommonService>();
        services.AddScoped<global::WoowApp.Services.Core.Service.ICommonService, global::WoowApp.Services.Core.Service.CommonService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.ConstSelectorService>();
        services.AddScoped<global::WoowApp.Services.Application.Managers.ControlDeviceManager>();
        services.AddSingleton<global::WoowApp.Services.Application.Models.ControlParams>();
        services.AddScoped<global::WoowApp.Services.Application.Managers.DeviceCacheManager>();
        services.AddTransient<global::WoowApp.Services.Application.Services.DeviceControlService>();
        services.AddSingleton<global::WoowApp.Services.Application.Managers.DeviceOperateTimeManager>();
        services.AddTransient<global::WoowApp.Services.Core.Services.Base.ExportManage>();
        services.AddTransient<global::WoowApp.Services.Core.Services.Base.IExportManage, global::WoowApp.Services.Core.Services.Base.ExportManage>();
        services.AddTransient<global::WoowApp.Services.Core.Services.Base.ImportManage>();
        services.AddTransient<global::WoowApp.Services.Core.Services.Base.IImportManage, global::WoowApp.Services.Core.Services.Base.ImportManage>();
        services.AddScoped<global::WoowApp.Services.Core.Filters.LogExceptionHandler>();
        services.AddScoped<global::WoowApp.Services.Core.Filters.LogOperateHandler>();
        services.AddScoped<global::WoowApp.Services.Application.Managers.PressureControlManager>();
        services.AddTransient<global::WoowApp.Services.Core.Service.RegionService>();
        services.AddScoped<global::WoowApp.Services.Application.Managers.SensorManager>();
        services.AddScoped<global::WoowApp.Services.Application.Models.ServiceContext>();
        services.AddScoped<global::WoowApp.Services.Web.Entry.Base.ServiceFactory>();
        services.AddScoped<global::WoowApp.Services.Shared.Base.IServiceFactory, global::WoowApp.Services.Web.Entry.Base.ServiceFactory>();
        services.AddScoped<global::WoowApp.Services.Application.Managers.SkidMountedManager>();
        services.AddScoped<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarClientFactory>();
        services.AddSingleton<global::WoowApp.Services.Core.Services.SysCacheService>();
        services.AddSingleton<global::WoowApp.Services.Core.Services.ISysCacheService, global::WoowApp.Services.Core.Services.SysCacheService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysConfigService>();
        services.AddTransient<global::WoowApp.Services.Core.SysConfigV2Service>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysDictDataService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysDictTypeService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysFileService>();
        services.AddTransient<global::WoowApp.Services.Core.SysFileV2Service>();
        services.AddSingleton<global::WoowApp.Services.Core.Managers.SysIdChangeManager>();
        services.AddTransient<global::WoowApp.Services.Core.SysLogExService>();
        services.AddTransient<global::WoowApp.Services.Core.SysLogOpService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysMenuService>();
        services.AddTransient<global::WoowApp.Services.Core.SysMenuV2Service>();
        services.AddTransient<global::WoowApp.Services.Core.SysMessageService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysOrgService>();
        services.AddTransient<global::WoowApp.Services.Core.SysOrgV2Service>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysPosService>();
        services.AddTransient<global::WoowApp.Services.Core.SysPosV2Service>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysRoleMenuService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysRoleOrgService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysRoleService>();
        services.AddTransient<global::WoowApp.Services.Core.SysRoleV2Service>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysUserExtOrgPosService>();
        services.AddTransient<global::WoowApp.Services.Core.SysUserGroupManageService>();
        services.AddTransient<global::WoowApp.Services.Core.SysUserGroupMemberManageService>();
        services.AddTransient<global::WoowApp.Services.Core.SysUserGroupSelectService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysUserOrgService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysUserRoleService>();
        services.AddTransient<global::WoowApp.Services.Core.SysUserSelectService>();
        services.AddTransient<global::WoowApp.Services.Core.Service.SysUserService>();
        services.AddTransient<global::WoowApp.Services.Core.SysUserV2Service>();
        services.AddScoped<global::WoowApp.Services.Core.UserManager>();
        services.AddScoped<global::WoowApp.Services.Core.IUserManager, global::WoowApp.Services.Core.UserManager>();
        services.AddScoped<global::WoowApp.Services.Core.Services.Auth.WoowApiAuthorzieService>();
        services.AddScoped<global::WoowApp.Base.WoowApiEnv>();
        services.AddScoped<global::WoowApp.Services.Application.WsChartService>();
        services.AddSingleton<global::WoowApp.Services.Application.WsClientContainer>();
        services.AddScoped<global::WoowApp.Services.Application.WsClientService>();
        services.AddScoped<global::WoowApp.Services.Core.WsConext>();
        services.AddScoped<global::WoowApp.Services.Core.WsDataService>();
        services.AddScoped<global::WoowApp.Services.Core.Services.Ws.WsUserMessagerService>();

    }

    static partial void AddOptionServices(this IServiceCollection services)
    {
        
        services.AddOptions<global::WoowApp.Options.JWTSettingsOptions>().BindConfiguration("JWTSettings");
        services.AddOptions<global::WoowApp.Options.WoowAppOptions>().BindConfiguration("WoowApp");
        services.AddOptions<global::WoowApp.Services.Core.AuthorityOptions>().BindConfiguration("Authority");
        services.AddOptions<global::WoowApp.Services.Core.CacheOptions>().BindConfiguration("Cache");
        services.AddOptions<global::WoowApp.Services.Core.CodeGenOptions>().BindConfiguration("CodeGen");
        services.AddOptions<global::WoowApp.Services.Core.Options.DbConnectionOptions>().BindConfiguration("DbConnection");
        services.AddOptions<global::WoowApp.Services.Core.OSSProviderOptions>().BindConfiguration("OSSProvider");
        services.AddOptions<global::WoowApp.Services.Core.PayCallBackOptions>().BindConfiguration("PayCallBack");
        services.AddOptions<global::WoowApp.Services.Core.RefreshTokenOptions>().BindConfiguration("RefreshToken");
        services.AddOptions<global::WoowApp.Services.Core.SnowIdOptions>().BindConfiguration("SnowId");
        services.AddOptions<global::WoowApp.Services.Core.UploadOptions>().BindConfiguration("Upload");
        services.AddOptions<global::WoowApp.Services.Core.WechatOptions>().BindConfiguration("Wechat");
        services.AddOptions<global::WoowApp.Services.Core.WechatPayOptions>().BindConfiguration("WechatPay");
    }
                               
    static partial void MapRegister()
    {
        
        new global::WoowApp.Services.Core.GetMenuListMenuItemV2Output_Mapper().Register(Mapster.TypeAdapterConfig.GlobalSettings);
        new global::WoowApp.Services.Core.Service.SysMenuMapper().Register(Mapster.TypeAdapterConfig.GlobalSettings);
        new global::WoowApp.Services.Core.SysMenuItemV2Output_Mapper().Register(Mapster.TypeAdapterConfig.GlobalSettings);
        new global::WoowApp.Services.Core.SysMenuV2Input_Mapper().Register(Mapster.TypeAdapterConfig.GlobalSettings);
        new global::WoowApp.Services.Core.SysRoleV2_SysMenuTreeOutput_Mapper().Register(Mapster.TypeAdapterConfig.GlobalSettings);
    }
    public static partial void MapType()
    {

Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.BuzzerStatusEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.ControlModeEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.HardwareVersionEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.LampStatusEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.LaserDirectionEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.LogDataActionEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.SensorTypeEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.DataScopeEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.EntitySyncStatusEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.Enums.GenderEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.Enums.JobStatusEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.Enums.MessageTypeEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.Enums.StatusEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.Enums.UserTypeEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.SexEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.YesNoCnEnum));
Enum.GetValues(typeof(global::WoowApp.Services.Core.YesNoEnum));


{ var _ = new Func<global::WoowApp.Services.Application.Enums.BuzzerStatusEnum, global::WoowApp.Services.Application.Enums.BuzzerStatusEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.BuzzerStatusEnum, global::WoowApp.Services.Application.Enums.BuzzerStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.BuzzerStatusEnum?, global::WoowApp.Services.Application.Enums.BuzzerStatusEnum>(u => (global::WoowApp.Services.Application.Enums.BuzzerStatusEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.BuzzerStatusEnum?, global::WoowApp.Services.Application.Enums.BuzzerStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum>(u => (global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.ControlModeEnum, global::WoowApp.Services.Application.Enums.ControlModeEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.ControlModeEnum, global::WoowApp.Services.Application.Enums.ControlModeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.ControlModeEnum?, global::WoowApp.Services.Application.Enums.ControlModeEnum>(u => (global::WoowApp.Services.Application.Enums.ControlModeEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.ControlModeEnum?, global::WoowApp.Services.Application.Enums.ControlModeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum, global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum, global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum?, global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum>(u => (global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum?, global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum>(u => (global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum, global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum, global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum?, global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum>(u => (global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum?, global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HardwareVersionEnum, global::WoowApp.Services.Application.Enums.HardwareVersionEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HardwareVersionEnum, global::WoowApp.Services.Application.Enums.HardwareVersionEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HardwareVersionEnum?, global::WoowApp.Services.Application.Enums.HardwareVersionEnum>(u => (global::WoowApp.Services.Application.Enums.HardwareVersionEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HardwareVersionEnum?, global::WoowApp.Services.Application.Enums.HardwareVersionEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum>(u => (global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum, global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum>(u => (global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum?, global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LampStatusEnum, global::WoowApp.Services.Application.Enums.LampStatusEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LampStatusEnum, global::WoowApp.Services.Application.Enums.LampStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LampStatusEnum?, global::WoowApp.Services.Application.Enums.LampStatusEnum>(u => (global::WoowApp.Services.Application.Enums.LampStatusEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LampStatusEnum?, global::WoowApp.Services.Application.Enums.LampStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LaserDirectionEnum, global::WoowApp.Services.Application.Enums.LaserDirectionEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LaserDirectionEnum, global::WoowApp.Services.Application.Enums.LaserDirectionEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LaserDirectionEnum?, global::WoowApp.Services.Application.Enums.LaserDirectionEnum>(u => (global::WoowApp.Services.Application.Enums.LaserDirectionEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LaserDirectionEnum?, global::WoowApp.Services.Application.Enums.LaserDirectionEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LogDataActionEnum, global::WoowApp.Services.Application.Enums.LogDataActionEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LogDataActionEnum, global::WoowApp.Services.Application.Enums.LogDataActionEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LogDataActionEnum?, global::WoowApp.Services.Application.Enums.LogDataActionEnum>(u => (global::WoowApp.Services.Application.Enums.LogDataActionEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.LogDataActionEnum?, global::WoowApp.Services.Application.Enums.LogDataActionEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum, global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum, global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum?, global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum>(u => (global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum?, global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorTypeEnum, global::WoowApp.Services.Application.Enums.SensorTypeEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorTypeEnum, global::WoowApp.Services.Application.Enums.SensorTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorTypeEnum?, global::WoowApp.Services.Application.Enums.SensorTypeEnum>(u => (global::WoowApp.Services.Application.Enums.SensorTypeEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SensorTypeEnum?, global::WoowApp.Services.Application.Enums.SensorTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum, global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum, global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum?, global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum>(u => (global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum?, global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.DataScopeEnum, global::WoowApp.Services.Core.DataScopeEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.DataScopeEnum, global::WoowApp.Services.Core.DataScopeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.DataScopeEnum?, global::WoowApp.Services.Core.DataScopeEnum>(u => (global::WoowApp.Services.Core.DataScopeEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.DataScopeEnum?, global::WoowApp.Services.Core.DataScopeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.EntitySyncStatusEnum, global::WoowApp.Services.Core.EntitySyncStatusEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.EntitySyncStatusEnum, global::WoowApp.Services.Core.EntitySyncStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.EntitySyncStatusEnum?, global::WoowApp.Services.Core.EntitySyncStatusEnum>(u => (global::WoowApp.Services.Core.EntitySyncStatusEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.EntitySyncStatusEnum?, global::WoowApp.Services.Core.EntitySyncStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.GenderEnum, global::WoowApp.Services.Core.Enums.GenderEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.GenderEnum, global::WoowApp.Services.Core.Enums.GenderEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.GenderEnum?, global::WoowApp.Services.Core.Enums.GenderEnum>(u => (global::WoowApp.Services.Core.Enums.GenderEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.GenderEnum?, global::WoowApp.Services.Core.Enums.GenderEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.JobStatusEnum, global::WoowApp.Services.Core.Enums.JobStatusEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.JobStatusEnum, global::WoowApp.Services.Core.Enums.JobStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.JobStatusEnum?, global::WoowApp.Services.Core.Enums.JobStatusEnum>(u => (global::WoowApp.Services.Core.Enums.JobStatusEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.JobStatusEnum?, global::WoowApp.Services.Core.Enums.JobStatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.MessageTypeEnum, global::WoowApp.Services.Core.Enums.MessageTypeEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.MessageTypeEnum, global::WoowApp.Services.Core.Enums.MessageTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.MessageTypeEnum?, global::WoowApp.Services.Core.Enums.MessageTypeEnum>(u => (global::WoowApp.Services.Core.Enums.MessageTypeEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.MessageTypeEnum?, global::WoowApp.Services.Core.Enums.MessageTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.StatusEnum, global::WoowApp.Services.Core.Enums.StatusEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.StatusEnum, global::WoowApp.Services.Core.Enums.StatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.StatusEnum?, global::WoowApp.Services.Core.Enums.StatusEnum>(u => (global::WoowApp.Services.Core.Enums.StatusEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.StatusEnum?, global::WoowApp.Services.Core.Enums.StatusEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.UserTypeEnum, global::WoowApp.Services.Core.Enums.UserTypeEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.UserTypeEnum, global::WoowApp.Services.Core.Enums.UserTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.UserTypeEnum?, global::WoowApp.Services.Core.Enums.UserTypeEnum>(u => (global::WoowApp.Services.Core.Enums.UserTypeEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.Enums.UserTypeEnum?, global::WoowApp.Services.Core.Enums.UserTypeEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.SexEnum, global::WoowApp.Services.Core.SexEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.SexEnum, global::WoowApp.Services.Core.SexEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.SexEnum?, global::WoowApp.Services.Core.SexEnum>(u => (global::WoowApp.Services.Core.SexEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.SexEnum?, global::WoowApp.Services.Core.SexEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoCnEnum, global::WoowApp.Services.Core.YesNoCnEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoCnEnum, global::WoowApp.Services.Core.YesNoCnEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoCnEnum?, global::WoowApp.Services.Core.YesNoCnEnum>(u => (global::WoowApp.Services.Core.YesNoCnEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoCnEnum?, global::WoowApp.Services.Core.YesNoCnEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoEnum, global::WoowApp.Services.Core.YesNoEnum>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoEnum, global::WoowApp.Services.Core.YesNoEnum?>(u => u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoEnum?, global::WoowApp.Services.Core.YesNoEnum>(u => (global::WoowApp.Services.Core.YesNoEnum)u); }
{ var _ = new Func<global::WoowApp.Services.Core.YesNoEnum?, global::WoowApp.Services.Core.YesNoEnum?>(u => u); }

        ConstSelectorService.GetEnumSelectors = () => [

EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.BuzzerStatusEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.CoilRegisterDeviceEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.ControlModeEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.DigitalOutputControlMethodEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.DiscreteRegisterDeviceEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.DistanceMeasurementTypeEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.HardwareVersionEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.HoldingRegisterDeviceEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.InputRegisterDeviceEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.LampStatusEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.LaserDirectionEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.LogDataActionEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.SensorConvertTypeEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.SensorTypeEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Application.Enums.SolenoidValveStatusEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.DataScopeEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.EntitySyncStatusEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.Enums.GenderEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.Enums.JobStatusEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.Enums.MessageTypeEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.Enums.StatusEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.Enums.UserTypeEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.SexEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.YesNoCnEnum)),
EnumHelper.EnumToSelectorDto(typeof(global::WoowApp.Services.Core.YesNoEnum)),
        ];
    }
}
