using Microsoft.AspNetCore.Diagnostics;
using System.Net;
using WoowApp.Services.Shared.FriendlyException;
using WoowApp.Services.Core.Models;
using WoowApp.Services.Shared.JsonSerialize;
using WoowApp.Services.Core.Filters;
using WoowApp.Services.Shared.Base;

namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// 异常拓展方法
/// </summary>
public static class ExceptionExtensions
{

    /// <summary>
    /// 全局异常处理
    /// </summary>
    /// <param name="app"></param>
    public static void UseWoowExceptionHandler(this IApplicationBuilder app)
    {
        app.UseExceptionHandler(appBuilder =>
        {
            appBuilder.Run(async context =>
            {
                var exceptionHandlerFeature = context.Features.Get<IExceptionHandlerFeature>();
                if (exceptionHandlerFeature != null)
                {
                    var exception = exceptionHandlerFeature.Error;
                    context.Response.ContentType = "application/json";

                    UnifyResult response;
                    if (exception is AppFriendlyException appFriendlyException)
                    {
                        context.Response.StatusCode = (int)HttpStatusCode.BadRequest; //(int)HttpStatusCode.OK; // 对于 UserFriendlyException 返回状态码 200

                        response = new UnifyResult//<object>
                        {
                            Code = appFriendlyException.Code ?? 200,
                            Message = appFriendlyException.Message
                        };
                    }
                    else if (exception is BadHttpRequestException badHttpRequestException)
                    {
                        context.Response.StatusCode = (int)HttpStatusCode.BadRequest;

                        response = new UnifyResult
                        {
                            Code = context.Response.StatusCode,
                            Type = "error",
                            Message = badHttpRequestException.Message
                        };
                    }
                    else
                    {
                        context.Response.StatusCode = (int)HttpStatusCode.BadRequest;

                        response = new UnifyResult
                        {
                            Code = 500,//context.Response.StatusCode,
                            Type = "error",

//#if DEBUG
                            Message = $"Internal Server Error. {exception.Message}"
//#else
//                            Message = "Internal Server Error"
//#endif

                        };
                    }
                    var responseJson = JSON.Serialize(response);
                    await context.Response.WriteAsync(responseJson);
                    try
                    {
                        var logExceptionHandler = Woow.GetService<LogExceptionHandler>()!;
                        await logExceptionHandler.OnExceptionAsync(exceptionHandlerFeature, context);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"LogExceptionHandler Exception：{ex}");
                    }
                }
            });
        });
    }

}