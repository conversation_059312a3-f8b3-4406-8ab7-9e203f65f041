using Microsoft.AspNetCore.Http.HttpResults;
using WoowApp.Services.Core.Models;

namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// 动态Api规范化返回扩展
/// </summary>
public static class ResultsExtensions
{
    /// <summary>
    /// 统一有值返回
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public static Ok<UnifyResult<T>> Ok<T>(T value)
    {
        var response = new UnifyResult<T>
        {
            Code = 200,
            Result = value,
        };

        return TypedResults.Ok(response);
    }

    /// <summary>
    /// 统一空返回
    /// </summary>
    /// <returns></returns>
    public static Ok<UnifyResult> Ok()
    {
        var response = new UnifyResult
        {
            Code = 200,
        };

        return TypedResults.Ok(response);
    }
}