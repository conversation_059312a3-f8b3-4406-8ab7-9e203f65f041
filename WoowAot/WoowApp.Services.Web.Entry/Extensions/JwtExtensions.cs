using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Text;
using WoowApp.Services.Core;
using WoowApp.Services.Core.Services.Auth;
using WoowApp.Services.Core.Utils;
using WoowApp.Services.Shared.Base;

namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// Jwt 扩展
/// </summary>
public static class JwtExtensions
{
    /// <summary>
    /// add jwt
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    public static IServiceCollection AddJwt(this IServiceCollection services, IConfigurationManager configuration)
    {

        var jwtSettings = JWTEncryption.GetJWTSettings();
        var securityKeyBytes = JWTEncryption.GetSecurityKeyBytes();

        services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        }).AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                // 验证签发方密钥
                ValidateIssuerSigningKey = jwtSettings.ValidateIssuerSigningKey ?? true,
                // 签发方密钥
                IssuerSigningKey = new SymmetricSecurityKey(securityKeyBytes),
                // 验证签发方
                ValidateIssuer = jwtSettings.ValidateIssuer ?? false,
                // 验证签收方
                ValidateAudience = jwtSettings.ValidateAudience ?? false,
                // 设置签发方
                ValidIssuer = jwtSettings.ValidIssuer,
                // 设置接收方
                ValidAudience = jwtSettings.ValidAudience,
                // 验证生存期
                ValidateLifetime = jwtSettings.ValidateLifetime ?? false,
                // 过期时间容错值
                ClockSkew = TimeSpan.FromSeconds(jwtSettings.ClockSkew ?? 5),
            };
        });

        services.AddAuthorization(options =>
        {
            options.AddPolicy("WoowApi", policy =>
            {
                policy.RequireAssertion(async context =>
                {
                    var service = Woow.GetService<WoowApiAuthorzieService>();
                    return await service!.CheckAuthorzieAsync(context);
                });
            });
            options.AddPolicy("Admin", policy => policy.RequireClaim(ClaimTypes.Role, "Admin"));
            options.AddPolicy("User", policy => policy.RequireClaim(ClaimTypes.Role, "User"));
        });

        return services;
    }
}