using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Refit;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Text.Json;
using WoowApp.Services.Application.Managers;
using WoowApp.Services.Application.Models;
using WoowApp.Services.Shared.Base;
using WoowApp.Services.Shared.FriendlyException;
using WoowApp.Services.Shared.JsonSerialize;

namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// IOC注入扩展方法
/// </summary>
public static partial class IocExtensions
{
    public static IServiceCollection AddIoc(this IServiceCollection services)
    {
        services.AddSingleton<IJsonSerializerProvider>(services =>
        {
            var provider = new JsonSerializerProvider();
            provider.Options.AddWoowJsonSerializerOptions();
            return provider;
        });

        services.AddTransient<ISerializerDataContractResolver>(sp =>
        {
            var opts = new JsonSerializerOptions();//(JsonSerializerDefaults.Web);
            opts.AddWoowJsonSerializerOptions();

            return new JsonSerializerDataContractResolver(opts);
        });

        services.AddSingleton<IMemoryCache, MemoryCache>();
        services.AddSingleton<IDistributedCache>(provider =>
        {
            //var cacheOptions = App.GetOptions<CacheOptions>();
            //if (cacheOptions.CacheType == CacheTypeEnum.RedisCache.ToString())
            //{
            //    var redisStr = $"{cacheOptions.RedisConnectionString},prefix={cacheOptions.InstanceName}";

            //    var redis = new CSRedisClient(redisStr);
            //    RedisHelper.Initialization(redis);
            //    return new CSRedisCache(redis);
            //}
            //else // 默认使用内存
            //{
            services.AddDistributedMemoryCache();
            IOptions<MemoryDistributedCacheOptions> options = provider.GetService<IOptions<MemoryDistributedCacheOptions>>();
            return new MemoryDistributedCache(options);
            //}
        });


        //// 注册OSS对象存储
        //services.AddOSSService(option =>
        //{
        //    var ossOptions = Woow.GetOptions<OSSProviderOptions>();
        //    option.Provider = (OSSProvider)ossOptions.Provider;
        //    option.Endpoint = ossOptions.Endpoint;
        //    option.AccessKey = ossOptions.AccessKey;
        //    option.SecretKey = ossOptions.SecretKey;
        //    option.Region = ossOptions.Region;
        //    option.IsEnableCache = ossOptions.IsEnableCache;
        //    option.IsEnableHttps = ossOptions.IsEnableHttps;
        //});

        services.AddOptionServices();
        services.AddServices();

        //services.AddRefitClient<IJobTransferApi>(provider => new RefitSettings()
        //{
        //    ContentSerializer = new SystemTextJsonContentSerializer(new JsonSerializerOptions().AddWoowJsonSerializerOptions())
        //}).ConfigureHttpClient((service, client) =>
        //{
        //    var serviceContext = Woow.GetService<ServiceContext>()!;
        //    var uploadUrl = serviceContext.CurrentJob.UploadUrl ?? throw Oops.Oh("请设置上传地址");
        //    client.BaseAddress = new Uri(uploadUrl);
        //});

        MapRegister();
        MapType();

        services.AddSingleton(typeof(InspectLogMananger<>));
        services.AddSingleton(typeof(TransferManager<>));
        services.AddScoped<IDeviceManager, DeviceManager>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        var scope = services.BuildServiceProvider().CreateScope();
        Woow.SetServiceFactory(scope.ServiceProvider.GetService<IServiceFactory>()!);
        return services;
    }

    static partial void AddServices(this IServiceCollection services);

    static partial void AddOptionServices(this IServiceCollection services);

    static partial void MapRegister();
    public static partial void MapType();
}
