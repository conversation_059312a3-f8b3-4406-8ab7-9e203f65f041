using SqlSugar;
using System.Reflection;
using WoowApp.Services.Core.Consts;
using WoowApp.Services.Core.DatabaseAccessor;

namespace WoowApp.Services.Web.Entry.Extensions;

public static partial class SqlSugarExtensions
{
    static partial void AotHelper()
    {
        List<Type> entityTypes = [
typeof(global::WoowApp.Services.Core.ApplicationConfig),
typeof(global::WoowApp.Services.Core.ApplicationConfigCategory),
typeof(global::WoowApp.Services.Core.ApplicationPrintTemplate),
typeof(global::WoowApp.Services.Core.ApplicationUserConfig),
typeof(global::WoowApp.Services.Core.Entities.SysUserGroup),
typeof(global::WoowApp.Services.Core.Entities.SysUserGroupMember),
typeof(global::WoowApp.Services.Core.SysConfig),
typeof(global::WoowApp.Services.Core.SysDataResource),
typeof(global::WoowApp.Services.Core.SysDictData),
typeof(global::WoowApp.Services.Core.SysDictType),
typeof(global::WoowApp.Services.Core.SysFile),
typeof(global::WoowApp.Services.Core.SysLogAudit),
typeof(global::WoowApp.Services.Core.SysLogDiff),
typeof(global::WoowApp.Services.Core.SysLogEx),
typeof(global::WoowApp.Services.Core.SysLogOp),
typeof(global::WoowApp.Services.Core.SysLogVis),
typeof(global::WoowApp.Services.Core.SysMenu),
typeof(global::WoowApp.Services.Core.SysOnlineUser),
typeof(global::WoowApp.Services.Core.SysOrg),
typeof(global::WoowApp.Services.Core.SysPos),
typeof(global::WoowApp.Services.Core.SysRegion),
typeof(global::WoowApp.Services.Core.SysRole),
typeof(global::WoowApp.Services.Core.SysRoleMenu),
typeof(global::WoowApp.Services.Core.SysRoleOrg),
typeof(global::WoowApp.Services.Core.SysTenant),
typeof(global::WoowApp.Services.Core.SysUser),
typeof(global::WoowApp.Services.Core.SysUserExtOrgPos),
typeof(global::WoowApp.Services.Core.SysUserMessage),
typeof(global::WoowApp.Services.Core.SysUserOrg),
typeof(global::WoowApp.Services.Core.SysUserRole)
            ];


        SqlSugarSetup.InitTableAndSeed = db =>
        {
            if (!entityTypes.Any()) return;
            // 初始化库表结构
            foreach (var entityType in entityTypes)
            {
                if (entityType == null) continue;
                var tAtt = entityType.GetCustomAttribute<TenantAttribute>();
                var provider = db.GetConnectionScope(tAtt == null ? SqlSugarConst.ConfigId : tAtt.configId);
                provider.CodeFirst.InitTables(entityType);
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysConfig>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysConfigSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysDataResource>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysDataResourceSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysDictData>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysDictDataSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysDictType>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysDictTypeSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysMenu>().Any())
            {
                var list = new global::WoowApp.Services.Application.SysMenuSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysMenu>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysMenuSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysOrg>().Any())
            {
                var list = new global::WoowApp.Services.Application.SysOrgSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysOrg>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysOrgSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysPos>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysPosSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysRegion>().Any())
            {
                var list = new global::WoowApp.Services.Core.Entity.SysRegionSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysRole>().Any())
            {
                var list = new global::WoowApp.Services.Application.SysRoleSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysRole>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysRoleSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysRoleMenu>().Any())
            {
                var list = new global::WoowApp.Services.Application.SysRoleMenuSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysRoleMenu>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysRoleMenuSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysTenant>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysTenantSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysUser>().Any())
            {
                var list = new global::WoowApp.Services.Application.SysUserSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysUser>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysUserSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysUserExtOrgPos>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysEmpExtOrgPosSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysUserOrg>().Any())
            {
                var list = new global::WoowApp.Services.Application.SysUserOrgSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysUserOrg>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysUserOrgSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysUserRole>().Any())
            {
                var list = new global::WoowApp.Services.Application.SysUserRoleSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }

            if (!db.QueryableWithAttr<global::WoowApp.Services.Core.SysUserRole>().Any())
            {
                var list = new global::WoowApp.Services.Core.SysUserRoleSeedData().HasData().ToList();
                if (list.Count > 0) db.InsertableWithAttr(list).ExecuteCommand();
            }
        };
    }
}
