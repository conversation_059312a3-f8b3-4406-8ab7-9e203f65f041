namespace WoowApp.Services.Web.Entry.Extensions;

/// <summary>
/// 实现swagger.json内存缓存中间件
/// </summary>
public static class SwaggerCacheMiddleware
{

    /// <summary>
    /// 实现swagger.json内存缓存
    /// </summary>
    /// <param name="app"></param>
    /// <returns></returns>
    public static IApplicationBuilder UseSwaggerCache(this IApplicationBuilder app)
    {
        var dictJson = new System.Collections.Concurrent.ConcurrentDictionary<string, byte[]>();
        app.Use(async (context, next) =>// cache swagger.json 
        {
            var path = context.Request.Path.ToString();
            if (path.StartsWith("/swagger/") && path.EndsWith("/swagger.json"))
            {
                if (dictJson.TryGetValue(path, out var bytes))
                {
                    context.Response.ContentType = "application/json;charset=utf-8";
                    await context.Response.Body.WriteAsync(bytes);
                }
                else
                {
                    var body = context.Response.Body;
                    using var stream = new MemoryStream();
                    context.Response.Body = stream;
                    await next.Invoke();
                    var result = stream.ToArray();
                    Console.WriteLine($"Result.Length = {result.Length}");
                    dictJson.TryAdd(path, result);
                    context.Response.Body = body;
                    await context.Response.Body.WriteAsync(result);
                }
            }
            else
            {
                await next.Invoke();
            }
        });
        return app;
    }
}