using WoowApp.Services.Shared.Base;

namespace WoowApp.Services.Web.Entry.Middlewares;

public static class WoowAppMiddleware
{

    /// <summary>
    /// 实现WoowApp桌面功能
    /// </summary>
    /// <param name="app"></param>
    /// <returns></returns>
    public static IApplicationBuilder UseWoowApp(this IApplicationBuilder app)
    {
        app.Use(async (context, next) =>
        {
            var configuration = app.ApplicationServices.GetService<IConfiguration>()!;
            //if (context.Request.Path.ToString().StartsWith("/api/") && configuration.GetValue("AppSettings:Desktop", false))
            //{
            //    context.Request.Path = context.Request.Path.ToString()[4..];
            //}
            var requestId = context.Request.Headers["x-scf-request-id"].ToString();
            //var index = 1;
            //Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss-ffff}]Start-{index}");
            context.Items["RequestId"] = string.IsNullOrWhiteSpace(requestId) ? Guid.NewGuid().ToString("N") : requestId;
            await next.Invoke();
            //Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss-ffff}]End-{index}");

            if (context.Response.StatusCode == 404 && configuration.GetValue("AppSettings:Desktop", false))
            {
                context.Response.StatusCode = 200;
                context.Request.Path = "/index.html";
                await next.Invoke();
            }
        });
        return app;
    }
}
