//using System.Diagnostics;
//using System.Text;
//using WoowApp.Services.Core;
//using WoowApp.Services.Shared.Base;

//namespace WoowApp.Services.Web.Entry.Middlewares;

///// <summary>
///// 日志中间件
///// </summary>
//public class RequestLoggingMiddleware
//{
//    private readonly RequestDelegate _next;
//    private readonly ILogger<RequestLoggingMiddleware> _logger;

//    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
//    {
//        _next = next;
//        _logger = logger;
//    }

//    public async Task InvokeAsync(HttpContext context)
//    {
//        var stopwatch = Stopwatch.StartNew();

//        // Log Request
//        context.Request.EnableBuffering();

//        var requestMethod = context.Request.Method;
//        var requestPath = context.Request.Path + context.Request.QueryString;

//        var headersText = FormatHeaders(context.Request.Headers);

//        string requestBodyText;
//        if (context.Request.ContentType != null && context.Request.ContentType.Contains("multipart/form-data", StringComparison.OrdinalIgnoreCase))
//        {
//            try
//            {
//                // 直接读取表单内容
//                var form = await context.Request.ReadFormAsync();
//                var fileNames = form.Files.Select(f => f.FileName).ToList();
//                requestBodyText = $"upload file(s): {string.Join(", ", fileNames)}";
//            }
//            catch (Exception ex)
//            {
//                requestBodyText = $"Error reading form: {ex.Message}";
//            }
//        }
//        else
//        {
//            // 非文件上传的情况，读取请求体
//            var requestBodyStream = new MemoryStream();
//            await context.Request.Body.CopyToAsync(requestBodyStream);
//            requestBodyStream.Seek(0, SeekOrigin.Begin);
//            requestBodyText = await new StreamReader(requestBodyStream).ReadToEndAsync();

//            // 将请求体的位置再次重置，允许后续中间件读取
//            requestBodyStream.Seek(0, SeekOrigin.Begin);
//            context.Request.Body = requestBodyStream;
//        }

//        //_logger.LogInformation("{httpRequestLog}", httpRequestLog);

//        // Intercept Response
//        var originalResponseBodyStream = context.Response.Body;
//        using var responseBodyStream = new MemoryStream();
//        context.Response.Body = responseBodyStream;

//        await _next(context);
//        stopwatch.Stop();

//        var userManager = Woow.GetService<IUserManager>(); 
//        var username = userManager?.UserName;// context.User.GetUsername();
//        var userId = userManager?.UserId;// context.User.GetUserId();

//        var httpRequestLog = $"HttpRequest: {requestMethod} {requestPath}\r\nRequestUser: {userId} --> {username}\r\nHeaders: {headersText}\r\nBody: {requestBodyText}\r\n";

//        responseBodyStream.Seek(0, SeekOrigin.Begin);
//        // var responseBodyText = await new StreamReader(responseBodyStream).ReadToEndAsync();
//        // var httpResponseLog = $"HttpResponse: {context.Response.StatusCode} \nBody: {responseBodyText}\nDuration: {stopwatch.ElapsedMilliseconds} ms\n";
//        var httpResponseLog = $"HttpResponse: {context.Response.StatusCode} \r\nDuration: {stopwatch.ElapsedMilliseconds} ms\r\n";
//        //_logger.LogInformation("{httpRequestLog}{httpResponseLog}", httpRequestLog, httpResponseLog);
//        _logger.LogInformation($"{httpRequestLog}{httpResponseLog}");
//        responseBodyStream.Seek(0, SeekOrigin.Begin);
//        await responseBodyStream.CopyToAsync(originalResponseBodyStream);
//        context.Response.Body = originalResponseBodyStream;
//    }

//    private static string FormatHeaders(IHeaderDictionary headers)
//    {
//        var builder = new StringBuilder();
//        foreach (var header in headers)//.Where(h => h.Key.StartsWith("Ezy")))
//        {
//            builder.Append($"[{header.Key}: {header.Value}] ");
//        }
//        return builder.ToString();
//    }
//}