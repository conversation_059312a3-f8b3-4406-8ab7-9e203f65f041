//if DEBUG
using Microsoft.OpenApi.Models;
using WoowApp.Services.Web.Entry.Filters;
//using Microsoft.AspNetCore.Builder;
//endif
using Serilog;
using Serilog.Events;
//using WoowApp.Services.Web.Entry.Middlewares;
using WoowApp.Services.Web.Entry.EndPoints;
using Microsoft.AspNetCore.Routing.Constraints;
using System.Diagnostics;
using WoowApp.Services.Web.Entry.Middlewares;
using WoowApp.Services.Application;
using WoowApp.Services.Application.Managers;
using WoowApp.Services.Core.DatabaseAccessor;
using WoowApp.Services.Shared.Base;

Stopwatch sw = Stopwatch.StartNew();
var builder = WebApplication.CreateSlimBuilder(args);
var configuration = builder.Configuration;
EndpointsMapper.ApiRoot = configuration["AppSettings:ApiRoot"] ?? "";

var services = builder.Services;

// 配置路由选项，注册正则表达式约束
services.Configure<RouteOptions>(options =>
{
    options.SetParameterPolicy<RegexInlineRouteConstraint>("regex");
});

services.AddHttpClient();

services.AddIoc();
services.AddWoowSerializerOptions();

#region Serilog 

// 配置 Serilog 日志
var logPath = Path.Combine(AppContext.BaseDirectory, "logs", "log-.log");

const string template = "[{Timestamp:HH:mm:ss.fff}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}";

// Configure Serilog with Console and File sinks
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console(restrictedToMinimumLevel: LogEventLevel.Information, outputTemplate: template)
    .WriteTo.File(logPath, rollingInterval: RollingInterval.Day, restrictedToMinimumLevel: LogEventLevel.Debug, outputTemplate: template)
    .CreateLogger();

// Replace default .NET logger with Serilog
builder.Host.UseSerilog();

var logger = Log.ForContext<Program>();

#endregion Serilog

// 添加 CORS 服务
services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy",
        policy => policy
            .AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader());
});

services.AddHttpContextAccessor();

services.AddJwt(configuration);

services.AddSqlSugar();

if (configuration.GetSection("AppSettings").GetValue("InjectOpenApiDoc", false))
{
    //if DEBUG

    // 注册 Swagger 服务
    services.AddEndpointsApiExplorer();
    services.AddSwaggerGen(options =>
    {
        options.SwaggerDoc("v1", new OpenApiInfo
        {
            Version = "v1",
            Title = "后台接口",
            Description = "网站描述",

        });

        options.SwaggerDoc("All Groups", new OpenApiInfo
        {
            Version = "All Groups",
            Title = "后台接口",
            Description = "网站描述",

        });

        // 添加 JWT Bearer Token 认证到 Swagger
        options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
        {
            Description = "JWT Authorization header using the Bearer scheme. \r\n\r\n " +
                      "Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\n" +
                      "Example: \"Bearer 12345abcdef\"",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = "Bearer"
        });

        // 设置全局的认证要求
        options.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header
            },
            new List<string>()
        }
        });

        options.EnableAnnotations();
        void IncludeXml(string file)
        {
            if (File.Exists(file))
            {
                options.IncludeXmlComments(file);
            }
        }

        IncludeXml(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"WoowApp.Services.Web.Entry.xml"));
        IncludeXml(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"WoowApp.Services.Application.xml"));
        IncludeXml(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, @"WoowApp.Services.Core.xml"));

        options.SchemaFilter<EnumSchemaFilter>();
        options.SchemaFilter<SnowflakeIdSchemaFilter>();
        options.OperationFilter<ObsoleteOperationFilter>();
    });
    //endif
}


// 注册后台托管服务
services.AddHostedService<SkidMountedService>();

builder.WebHost.UseKestrelHttpsConfiguration();

builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.Limits.MaxRequestBodySize = 1024 * 1024 * 1000; // 1000MB
});

var app = builder.Build();

if (configuration.GetSection("AppSettings").GetValue("InjectOpenApiDoc", false))
{
    ////if DEBUG
    app.UseSwaggerCache();
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        options.SwaggerEndpoint("/swagger/v1/swagger.json", "v1");
        options.SwaggerEndpoint("/swagger/All Groups/swagger.json", "All Groups");
    });
    //endif
}

app.UseWoowApp();

app.UseStaticFiles();

app.UseWoowService();
// 启用跨域
app.UseCors("CorsPolicy");

// 全局异常处理
app.UseWoowExceptionHandler();

app.UseAuthentication();
app.UseAuthorization();

// 映射所有接口
app.MapEndpoints();

app.UseWebSockets();
app.MapGroup(EndpointsMapper.ApiRoot).MapWs();

app.Lifetime.ApplicationStarted.Register(() =>
{
    Console.WriteLine($"WebStartTime={sw.Elapsed.TotalMilliseconds}ms");
});
app.Run();

