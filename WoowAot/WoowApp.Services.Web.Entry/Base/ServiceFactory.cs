using WoowApp.Services.Shared.Base;

namespace WoowApp.Services.Web.Entry.Base;

/// <summary>
/// 注入服务工厂
/// </summary>
public class ServiceFactory : IServiceFactory, IScoped
{
    private readonly IServiceProvider serviceProvider;

    public ServiceFactory(IServiceProvider serviceProvider)
    {
        this.serviceProvider = serviceProvider;
    }
    public T GetService<T>()
    {
        return serviceProvider.GetService<T>()!;
    }
}
