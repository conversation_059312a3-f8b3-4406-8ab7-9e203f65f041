<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<InvariantGlobalization>False</InvariantGlobalization>
		<!--<PublishAot>true</PublishAot>-->
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<PreserveCompilationContext>true</PreserveCompilationContext>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
		<EmitCompilerGeneratedFiles>True</EmitCompilerGeneratedFiles>
		<EnableRequestDelegateGenerator>true</EnableRequestDelegateGenerator>
		<DocumentationFile></DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="wwwroot\**" />
	  <Content Remove="wwwroot\**" />
	  <EmbeddedResource Remove="wwwroot\**" />
	  <None Remove="wwwroot\**" />
	</ItemGroup>
	
	<ItemGroup>
		<RdXmlFile Include="rd.xml" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\WoowApp.Services.Application\WoowApp.Services.Application.csproj" />
	</ItemGroup>
	
	<ItemGroup>
		<PackageReference Include="WoowApp.Analyzers" Version="1.0.37">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<Analyzer Include="$(NuGetPackageRoot)WoowApp.Analyzers\1.0.37\lib\netstandard2.0\WoowApp.Analyzers.dll" OutputItemType="Analyzer" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.12.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.8" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
		<PackageReference Include="System.Private.Uri" Version="4.3.2" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
	</ItemGroup>
	
</Project>
