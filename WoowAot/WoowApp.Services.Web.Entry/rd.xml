<Directives>
	<Application>
		<Assembly Name="WoowApp.Services.Web.Entry"  Dynamic="Required All" />
		<Assembly Name="WoowApp.Services.Application"  Dynamic="Required All" />
		<Assembly Name="WoowApp.Services.Core"  Dynamic="Required All" />
		<Assembly Name="WoowApp.Services.Shared"  Dynamic="Required All" />
		<Assembly Name="SqlSugar" Dynamic="Required All" />
		<Assembly Name="Mapster" Dynamic="Required All" />
		<Assembly Name="Microsoft.Extensions.Logging.Abstractions"  Dynamic="Required All" />
		<Assembly Name="Swashbuckle.AspNetCore.Swagger"  Dynamic="Required All" />
		<Assembly Name="Swashbuckle.AspNetCore.SwaggerGen"  Dynamic="Required All" />
		<Assembly Name="Swashbuckle.AspNetCore.SwaggerUI"  Dynamic="Required All" />
		<Assembly Name="Swashbuckle.AspNetCore.Annotations"  Dynamic="Required All" />
	</Application>
</Directives>