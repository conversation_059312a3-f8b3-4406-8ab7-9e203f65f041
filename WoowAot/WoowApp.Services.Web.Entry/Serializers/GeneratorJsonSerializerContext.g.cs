
// <auto-generated/>
using System.Text.Json.Serialization;

namespace WoowApp.Services.Web.Entry.Serializers;

[JsonSerializable(typeof(global::WoowApp.Services.Core.UserMessageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.WsClientInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DynamicContentOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.WsClientOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UserMessageDataOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DynamicContentItemOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Models.ControlParams))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.GetMenuListMenuItemV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Web.Entry.Models.ValidationResponse))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.GetMenuListMenuItemMetaV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Managers.Dto.SensorDataOutput))]
[JsonSerializable(typeof(IEnumerable<global::WoowApp.Services.Core.GetMenuListMenuItemV2Output>))]
public partial class GeneratorJsonSerializerContext : JsonSerializerContext;
