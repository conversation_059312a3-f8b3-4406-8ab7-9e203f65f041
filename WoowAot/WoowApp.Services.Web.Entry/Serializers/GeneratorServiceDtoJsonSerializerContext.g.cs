
// <auto-generated/>
using System.Text.Json;
using System.Text.Json.Serialization;

namespace WoowApp.Services.Web.Entry.Serializers;
                                 
public static class DtoSerializerExtensions
{
    public static JsonSerializerOptions AddWoowDtoSerializerOptions(this JsonSerializerOptions serializerOptions)
    {
        serializerOptions.TypeInfoResolverChain.Insert(0, GeneratorServiceDtoJsonSerializerContext.Default);
        return serializerOptions;
    }
}


[JsonSerializable(typeof(global::Microsoft.AspNetCore.Http.IFormFile))]
[JsonSerializable(typeof(global::Microsoft.AspNetCore.Mvc.FileContentResult))]
[JsonSerializable(typeof(global::Microsoft.AspNetCore.Mvc.FileStreamResult))]
[JsonSerializable(typeof(global::Microsoft.AspNetCore.Mvc.IActionResult))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.ApplicationPrintTemplateManageOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Base.DropdownItemOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Dto.ApplicationConfig_ApplicationConfigCategoryTreeOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Dto.ApplicationConfigCategory_ApplicationConfigCategoryTreeOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Dto.SysUserSelect_SysOrgTreeOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.Region_GetAllRegionOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.Region_SysRegionTreeOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.RoleOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.SelectorDto>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysDictData>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysDictType>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysFile>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysMenu>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysMenuItemV2Output>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysOrgV2_SysOrgTreeOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysRoleV2_SysMenuTreeOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysUserV2_SysOrgTreeOutput>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<long>))]
[JsonSerializable(typeof(global::System.Collections.Generic.List<string>))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlCollectInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlCollectOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlGetBuzzerStateInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlGetBuzzerStateOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlGetLampStateInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlGetLampStateOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlGetSolenoidValveStateInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlGetSolenoidValveStateOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlSetBuzzerStateInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlSetControlParamsInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlSetControlParamsOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlSetControlSignalInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlSetLampStateInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Application.Service.Dto.DeviceControlSetSolenoidValveStateInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddApplicationPrintTemplateManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddApplicationUserConfigManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddSysConfigV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddSysMenuV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddSysOrgV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddSysPosV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddSysRoleV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.AddSysUserV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.ApplicationPrintTemplateManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.ApplicationPrintTemplateManageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.ApplicationUserConfigManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.ApplicationUserConfigManageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Base.BaseApiSelectInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Base.DropdownItemOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteApplicationPrintTemplateManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteApplicationUserConfigManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteSysConfigV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteSysMenuV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteSysOrgV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteSysPosV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteSysRoleV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.BatDeleteSysUserV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.ApplicationUserConfigManageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.ApplicationConfigCategoryOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.ApplicationConfigOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysMessageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserGroupManageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserGroupMemberManageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserGroupSelectOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserSelectOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Service.RegionOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysConfigV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysDictData>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysDictType>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysFile>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysFileV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysLogExOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysLogOpOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysOrgV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysPosV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysRole>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysRoleV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysUserV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DeleteApplicationPrintTemplateManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DeleteSysConfigV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DeleteSysMenuV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DeleteSysOrgV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DeleteSysPosV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DeleteSysRoleV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.DeleteSysUserV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.AddApplicationConfigCategoryInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.AddApplicationConfigInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.AddSysMessageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.AddSysUserGroupManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.AddSysUserGroupMemberManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.ApplicationConfig_ApplicationConfigCategoryTreeOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.ApplicationConfigCategory_ApplicationConfigCategoryTreeOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.ApplicationConfigCategoryInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.ApplicationConfigCategoryOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.ApplicationConfigInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.ApplicationConfigOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.BatDeleteApplicationConfigCategoryInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.BatDeleteApplicationConfigInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.BatDeleteSysUserGroupManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.BatDeleteSysUserGroupMemberManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.BatHandleSysMessageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.DeleteApplicationConfigCategoryInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.DeleteApplicationConfigInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QueryApplicationConfigCategoryInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QueryApplicationConfigCategoryOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QueryApplicationConfigInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QueryApplicationConfigOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysMessageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysMessageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserGroupManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserGroupManageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserGroupMemberManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserGroupMemberManageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserGroupSelectInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserGroupSelectOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserSelectInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.QuerySysUserSelectOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysMessageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysMessageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserGroupManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserGroupManageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserGroupMemberManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserGroupMemberManageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserGroupSelectInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserGroupSelectOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserSelect_SysOrgTreeOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserSelectInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.SysUserSelectOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.UpdateApplicationConfigCategoryInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.UpdateApplicationConfigInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.UpdateSysMessageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.UpdateSysUserGroupManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Dto.UpdateSysUserGroupMemberManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.GetMenuListV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::Microsoft.AspNetCore.Mvc.FileContentResult>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::Microsoft.AspNetCore.Mvc.FileStreamResult>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::Microsoft.AspNetCore.Mvc.IActionResult>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.ApplicationPrintTemplateManageOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Base.DropdownItemOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Dto.ApplicationConfig_ApplicationConfigCategoryTreeOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Dto.ApplicationConfigCategory_ApplicationConfigCategoryTreeOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Dto.SysUserSelect_SysOrgTreeOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.Region_GetAllRegionOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.Region_SysRegionTreeOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.RoleOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.Service.SelectorDto>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysDictData>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysDictType>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysFile>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysMenu>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysMenuItemV2Output>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysOrgV2_SysOrgTreeOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysRoleV2_SysMenuTreeOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<global::WoowApp.Services.Core.SysUserV2_SysOrgTreeOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<long>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::System.Collections.Generic.List<string>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Application.Service.Dto.DeviceControlCollectOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Application.Service.Dto.DeviceControlGetBuzzerStateOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Application.Service.Dto.DeviceControlGetLampStateOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Application.Service.Dto.DeviceControlGetSolenoidValveStateOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Application.Service.Dto.DeviceControlSetControlParamsOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.ApplicationUserConfigManageOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.ApplicationConfigCategoryOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.ApplicationConfigOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysMessageOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserGroupManageOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserGroupMemberManageOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserGroupSelectOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Dto.SysUserSelectOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.Service.RegionOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysConfigV2Output>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysDictData>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysDictType>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysFile>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysFileV2Output>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysLogExOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysLogOpOutput>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysOrgV2Output>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysPosV2Output>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysRole>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysRoleV2Output>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.DatabaseAccessor.SqlSugarPagedList<global::WoowApp.Services.Core.SysUserV2Output>>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Dto.QueryApplicationConfigCategoryOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Dto.QueryApplicationConfigOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Dto.QuerySysMessageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Dto.QuerySysUserGroupManageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Dto.QuerySysUserGroupMemberManageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Dto.QuerySysUserGroupSelectOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Dto.QuerySysUserSelectOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.GetMenuListV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QueryApplicationUserConfigManageOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysConfigV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysFileV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysLogExOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysLogOpOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysOrgV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysPosV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysRoleV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.QuerySysUserV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Service.FileOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Service.GetVerificationInfoOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Service.LoginUserInfoOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Service.LoginV2Output>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.Service.QueryRegionOutput>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.SysDictData>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<global::WoowApp.Services.Core.SysDictType>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<int>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Models.UnifyResult<string>))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.OwnMenuSysRoleV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QueryApplicationUserConfigManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QueryApplicationUserConfigManageOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysConfigV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysConfigV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysFileV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysFileV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysLogExInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysLogExOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysLogOpInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysLogOpOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysOrgV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysOrgV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysPosV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysPosV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysRoleV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysRoleV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysUserV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.QuerySysUserV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.AddDictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.AddDictTypeInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.AddRegionInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.AddRoleInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.BatDeleteRegionInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.ChageStatusDictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.ChangePwdInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.ChangeStatusDictTypeInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.DeleteDictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.DeleteDictTypeInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.DeleteFileInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.DeleteRegionInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.DeleteRoleInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.DictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.DictTypeInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.FileInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.FileOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.GetDataDictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.GetDataDictTypeInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.GetVerificationInfoOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.LoginUserInfoOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.LoginV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.LoginV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.PageDictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.PageDictTypeInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.PageFileInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.PageRoleInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.QueryDictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.QueryRegionInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.QueryRegionOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.Region_GetAllRegionOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.Region_SysRegionTreeOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.RegionInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.RegionOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.ResetPwdUserInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.RoleInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.RoleMenuInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.RoleOrgInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.RoleOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.SearchRegionInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.SelectorDto))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.UpdateDictDataInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.UpdateDictTypeInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.UpdateLoginUserInfoInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.UpdateRegionInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.Service.UpdateRoleInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SetSysRoleStatusInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SetSysUserStatusInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysConfigV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysConfigV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysDictData))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysDictType))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysFile))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysFileV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysFileV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysLogExInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysLogExOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysLogOpInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysLogOpOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysMenu))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysMenuItemV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysMenuV2NodeDropInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysOrgV2_SysOrgTreeOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysOrgV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysOrgV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysPosV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysPosV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysRole))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysRoleV2_SysMenuTreeOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysRoleV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysRoleV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysUserV2_SysOrgTreeOutput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysUserV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.SysUserV2Output))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateApplicationPrintTemplateManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateApplicationUserConfigManageInput))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateSysConfigV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateSysMenuV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateSysOrgV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateSysPosV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateSysRoleV2Input))]
[JsonSerializable(typeof(global::WoowApp.Services.Core.UpdateSysUserV2Input))]
[JsonSerializable(typeof(int))]
[JsonSerializable(typeof(long))]
[JsonSerializable(typeof(string))]
public partial class GeneratorServiceDtoJsonSerializerContext : JsonSerializerContext;public partial class GeneratorServiceDtoJsonSerializerContext : JsonSerializerContext;
