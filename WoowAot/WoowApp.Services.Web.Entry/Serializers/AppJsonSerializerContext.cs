using Microsoft.AspNetCore.Mvc;
using System.Text.Json.Serialization;
using WoowApp.Services.Application.Entities;
using WoowApp.Services.Application.Managers.Dto;
using WoowApp.Services.Application.Models;
using WoowApp.Services.Core;
using WoowApp.Services.Core.Base;
using WoowApp.Services.Core.Enums;
using WoowApp.Services.Core.Models;
using WoowApp.Services.Core.Service;

namespace WoowApp.Services.Web.Entry.Serializers;
//if DEBUG
[JsonSerializable(typeof(Swashbuckle.AspNetCore.SwaggerUI.ConfigObject))]
[JsonSerializable(typeof(Swashbuckle.AspNetCore.SwaggerUI.OAuthConfigObject))]
[JsonSerializable(typeof(Swashbuckle.AspNetCore.SwaggerUI.InterceptorFunctions))]
//endif
[JsonSerializable(typeof(UnifyResult))]
[JsonSerializable(typeof(ProblemDetails))]
[JsonSerializable(typeof(BasePageInput))]

//[JsonSerializable(typeof(GenderEnum))]
[JsonSerializable(typeof(object))]
[JsonSerializable(typeof(SysLogOp))]
[JsonSerializable(typeof(LoginRole))]
[JsonSerializable(typeof(List<LoginRole>))]

[JsonSerializable(typeof(LogData<string>))]
[JsonSerializable(typeof(ValveOperatorStation))]
[JsonSerializable(typeof(ValveOperatorStationSensor))]
//[JsonSerializable(typeof(ValveJobManagerSensorOutput))]
//[JsonSerializable(typeof(ValveJobManagerSensorOutput[]))]
[JsonSerializable(typeof(List<ValveOperatorStationSensor>))]
//[JsonSerializable(typeof(List<ValveJobManagerSensorOutput>))]
//[JsonSerializable(typeof(List<List<ValveJobManagerSensorOutput>>))]
[JsonSerializable(typeof(LogData<string>))]

[JsonSerializable(typeof(DeviceManagerOutput<ushort[]>))]
[JsonSerializable(typeof(DeviceManagerOutput<bool[]>))]
[JsonSerializable(typeof(DeviceManagerOutput<bool>))]
[JsonSerializable(typeof(DeviceManagerOutput<List<decimal>>))]
[JsonSerializable(typeof(DeviceManagerOutput<List<SerialPortModel>>))]
[JsonSerializable(typeof(DeviceManagerGetValuesInput))]
[JsonSerializable(typeof(DeviceManagerSetValueInput<bool>))]
[JsonSerializable(typeof(DeviceManagerSetSerialPortInput))]
[JsonSerializable(typeof(DeviceManagerSetValueInput<ushort>))]
[JsonSerializable(typeof(DeviceManagerSetValueInput<byte>))]

[JsonSerializable(typeof(UnifyResult<>))]
//[JsonSerializable(typeof(UnifyResult<List<ProductProcessInspectionItemInfo>>))]
public partial class AppJsonSerializerContext : JsonSerializerContext
{
}
