{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "AppSettings": {
    "ApiRoot": "",
    "Desktop": false,
    "InjectOpenApiDoc": true,
    "RunSkidMounted": true
  },
  //"Authority": {
  //  "UserPwdKey": ""
  //},
  "JWTSettings": {
    "ValidateIssuerSigningKey": true, // 是否验证密钥，bool 类型，默认true
    "IssuerSigningKey": "1C382930-A3D2-D741-EFB8-065C374B1C9ABCDWE", // 密钥，string 类型，必须是复杂密钥，长度大于16
    "ValidateIssuer": false, // 是否验证签发方，bool 类型，默认true
    "ValidIssuer": "GSDT", // 签发方，string 类型
    "ValidateAudience": false, // 是否验证签收方，bool 类型，默认true
    "ValidAudience": "GSDT", // 签收方，string 类型
    "ValidateLifetime": true, // 是否验证过期时间，bool 类型，默认true，建议true
    "ExpiredTime": 100800, // 过期时间，long 类型，单位分钟，默认20分钟
    "ClockSkew": 15 // 过期时间容错值，long 类型，单位秒，默认5秒
  },
  "RefreshToken": {
    "ExpiredTime": 20160 // 过期时间单位分钟（一般 refresh_token 的有效时间 > 2 * access_token 的有效时间）
  },
  //"DbConnection": {
  //  "EnableInitTable": false, // 启用初始化库表
  //  "EnableAutoCreateDb": false,
  //  "EnableDiffLog": false, // 启用库表差异日志
  //  "ConnectionConfigs": [ // 默认第一个为主库
  //    {
  //      "ConfigId": "main",
  //      "DbType": "Sqlite", // MySql、SqlServer、Sqlite、Oracle、PostgreSQL、Dm、Kdbndp、Oscar、MySqlConnector、Access
  //      "ConnectionString": "DataSource=main_data.db;Password=E32CE1F29B6745F984DA86FBC0159F9F",
  //      "IsAutoCloseConnection": true
  //    }
  //  ]
  //}
}
