using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json.Linq;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.ComponentModel;
using System.Reflection;
using WoowApp.Services.Core.Attributes;
using WoowApp.Services.Core.Utils;

namespace WoowApp.Services.Web.Entry.Filters;

/// <summary>
/// Enum OpenApi 扩展
/// </summary>
public class EnumSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (context.Type.IsEnum)
        {
            schema.Enum.Clear();

            var infoList = EnumHelper.GetEnumItemInfos(context.Type);
            foreach (var info in infoList)
            {
                schema.Enum.Add(new OpenApiLong(info.Value));
            }
            schema.Type = "integer";

            var constSelector = context.Type.GetCustomAttribute<ConstSelectorAttribute>();

            schema.Description = $"{constSelector?.Name ?? context.Type.Name}<br />&nbsp;{string.Join("<br />", infoList.Select(u => $"{(string.IsNullOrWhiteSpace(u.Desc) || u.Desc == u.Name ? "" : u.Desc + " ")}{u.Name}={u.Value}"))}";
            schema.Format = "int32";
        }
    }
}