using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using WoowApp.Services.Web.Entry.Models;

namespace WoowApp.Services.CodeGen.Entry.Filters;

/// <summary>
/// 全局数据机校验
/// </summary>
[RequiresUnreferencedCode("validation filter")]
public class GlobalValidationFilter
{
    internal static EndpointFilterDelegate Validate(EndpointFilterFactoryContext context, EndpointFilterDelegate next)
    {
        var parameters = context.MethodInfo.GetParameters();

        if (parameters.Length == 0)
        {
            return next;
        }

        return async invocationContext =>
        {
            var validationErrors = new Dictionary<string, string[]>();
            var arguments = invocationContext.Arguments;

            for (var i = 0; i < parameters.Length; i++)
            {
                var parameterInfo = parameters[i];
                var argument = arguments[i];

                // 获取参数上的验证属性
                var validationAttributes = parameterInfo.GetCustomAttributes<ValidationAttribute>(true);

                // 验证参数上的验证属性
                ValidateAttributes(validationAttributes, argument, parameterInfo, validationErrors);

                // 如果参数是复杂类型，继续使用 Validator.TryValidateObject
                if (argument != null && !(argument.GetType().IsPrimitive || argument is string))
                {
                    // 验证复杂对象
                    ValidateObject(argument, validationErrors);
                }
            }

            if (validationErrors.Any())
            {
                // 返回验证错误结果
                return Results.Problem(new ValidationResponse(validationErrors)
                {
                    Title = "One or more validation errors occurred.",
                    Status = StatusCodes.Status422UnprocessableEntity,
                    Detail = "See the errors property for details.",
                    Instance = invocationContext.HttpContext.Request.Path
                });
            }

            return await next(invocationContext);
        };
    }

    private static void ValidateObject(object argument, Dictionary<string, string[]> validationErrors)
    {
        var validationContext = new ValidationContext(argument);
        var validationResults = new List<ValidationResult>();

        var valid = Validator.TryValidateObject(argument, validationContext, validationResults, true);

        if (valid)
        {
            return;
        }

        foreach (var validationResult in validationResults)
        {
            var errorMessage = validationResult.ErrorMessage ?? "Validation error";
            foreach (var memberName in validationResult.MemberNames)
            {
                if (!validationErrors.ContainsKey(memberName))
                {
                    validationErrors[memberName] = [errorMessage];
                }
                else
                {
                    var errors = validationErrors[memberName].ToList();
                    errors.Add(errorMessage);
                    validationErrors[memberName] = errors.ToArray();
                }
            }
        }
    }

    private static void ValidateAttributes(IEnumerable<ValidationAttribute> validationAttributes, object? argument, ParameterInfo parameterInfo,
        Dictionary<string, string[]> validationErrors)
    {
        var memberName = parameterInfo.Name ?? "该字段";
        var context = new ValidationContext(parameterInfo) { MemberName = memberName };

        foreach (var validationAttribute in validationAttributes)
        {
            // 执行验证
            var isValid = validationAttribute.IsValid(argument);
            if (isValid)
            {
                continue;
            }

            var validationResult = validationAttribute.GetValidationResult(argument, context);
            var errorMessage = validationResult?.ErrorMessage ?? "Validation error";

            if (!validationErrors.ContainsKey(memberName))
            {
                validationErrors[memberName] = [errorMessage];
            }
            else
            {
                var errors = validationErrors[memberName].ToList();
                errors.Add(errorMessage);
                validationErrors[memberName] = errors.ToArray();
            }
        }
    }
}
