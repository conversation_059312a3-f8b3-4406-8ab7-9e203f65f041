//if DEBUG
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using WoowApp.Services.Shared.Snowflake;

namespace WoowApp.Services.Web.Entry.Filters;

/// <summary>
/// OpenApi 雪花Id long转string 
/// </summary>
public class SnowflakeIdSchemaFilter : ISchemaFilter
{
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        foreach (var property in context.Type.GetProperties())
        {
            var attrs = property.GetCustomAttributes(typeof(SnowflakeIdAttribute), true);
            if (attrs.Any())
            {
                // Convert property name to camel case
                var propertyName = property.Name.ToCamelCase();
                if (schema.Properties.ContainsKey(propertyName))
                {
                    var propSchema = schema.Properties[propertyName];

                    if (IsEnumerableOfLong(property.PropertyType) || property.PropertyType == typeof(long[]) || property.PropertyType == typeof(List<long>))
                    {
                        // If the property is IEnumerable<long>, change the item type to string
                        propSchema.Type = "array";
                        propSchema.Items = new OpenApiSchema
                        {
                            Type = "string",
                            Example = new OpenApiString("1")
                        };
                    }
                    else if (property.PropertyType == typeof(long))
                    {
                        propSchema.Type = "string";
                        propSchema.Example = new OpenApiString("1");
                    }
                    else if (property.PropertyType == typeof(long?))
                    {
                        propSchema.Type = "string";
                        propSchema.Example = new OpenApiString("1");
                    }
                    else if (property.PropertyType == typeof(int))
                    {
                        propSchema.Type = "string";
                        propSchema.Example = new OpenApiString("1");
                    }
                    else if (property.PropertyType == typeof(int?))
                    {
                        propSchema.Type = "string";
                        propSchema.Example = new OpenApiString("1");
                    }
                }
            }
        }
    }

    private static bool IsEnumerableOfLong(Type type)
    {
        if (type.IsGenericType && typeof(IEnumerable<>).IsAssignableFrom(type.GetGenericTypeDefinition()))
        {
            var itemType = type.GetGenericArguments()[0];
            return itemType == typeof(long);
        }

        return false;
    }
}


//endif