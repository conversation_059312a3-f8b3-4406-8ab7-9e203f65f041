//if DEBUG
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace WoowApp.Services.Web.Entry.Filters;

/// <summary>
/// 过时方法OpenApi处理
/// </summary>
public class ObsoleteOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var deprecated = operation.Deprecated;

        if (deprecated)
        {
            var obsoleteAttr = context.MethodInfo.GetCustomAttribute<ObsoleteAttribute>();
            operation.Description = obsoleteAttr?.Message;
        }
    }
}

//endif