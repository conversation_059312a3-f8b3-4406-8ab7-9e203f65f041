using Microsoft.AspNetCore.Mvc;
using WoowApp.Services.Core.Extensions;
using WoowApp.Services.Shared.JsonSerialize;

namespace WoowApp.Services.Web.Entry.Models;

[JsonSerializeRegister]
public class ValidationResponse : ValidationProblemDetails
{
    public ValidationResponse(Dictionary<string, string[]> errors) : base(errors)
    {

    }

    public ValidationResponse()
    {

    }

    public int? Code => Status;

    public string? Message => Detail;

    public long Timestamp => DateTime.UtcNow.ToLong();
}