
// <auto-generated/>
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using WoowApp.Services.Web.Entry.Extensions;
using WoowApp.Services.Web.Entry.Filters;
using WoowApp.Services.Shared.Base;
//if DEBUG
using Swashbuckle.AspNetCore.Annotations;
//endif

namespace WoowApp.Services.Web.Entry.EndPoints;

public static partial class EndpointsMapper
{
    static partial void MapGeneratorEndpoints(this IEndpointRouteBuilder builder)
    {
         
      {
        var applicationConfigCategoryApi = builder.MapGroup(ApiRoot).WithTags("ApplicationConfigCategory 应用配置分类");

        applicationConfigCategoryApi.MapPost("/ApplicationConfigCategory/add",  [SwaggerOperation(Summary = "增加应用配置分类", OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [FromBody]global::WoowApp.Services.Core.Dto.AddApplicationConfigCategoryInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapGet("/ApplicationConfigCategory/ApplicationConfigCategoryTree",  [SwaggerOperation(Summary = "获取父级树列表", OperationId = "ApplicationConfigCategoryTree")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "ApplicationConfigCategoryTreeAsync" });
            var result = await service.ApplicationConfigCategoryTreeAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapPost("/ApplicationConfigCategory/delete",  [SwaggerOperation(Summary = "批量删除应用配置分类", OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [FromBody]global::WoowApp.Services.Core.Dto.BatDeleteApplicationConfigCategoryInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapDelete("/ApplicationConfigCategory/delete",  [SwaggerOperation(Summary = "删除应用配置分类", OperationId = "deleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [AsParameters]global::WoowApp.Services.Core.Dto.DeleteApplicationConfigCategoryInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapPost("/ApplicationConfigCategory/export",  [SwaggerOperation(Summary = "应用配置分类导出", OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [FromBody]global::WoowApp.Services.Core.Dto.ApplicationConfigCategoryInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapGet("/ApplicationConfigCategory/detail",  [SwaggerOperation(Summary = "获取应用配置分类", OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [AsParameters] global::WoowApp.Services.Core.Dto.QueryApplicationConfigCategoryInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapPost("/ApplicationConfigCategory/import",  [SwaggerOperation(Summary = "应用配置分类模板导入", OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapPost("/ApplicationConfigCategory/importTemplate",  [SwaggerOperation(Summary = "应用配置分类导入模版下载", OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapPost("/ApplicationConfigCategory/page",  [SwaggerOperation(Summary = "分页查询应用配置分类", OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [FromBody]global::WoowApp.Services.Core.Dto.ApplicationConfigCategoryInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigCategoryApi.MapPost("/ApplicationConfigCategory/edit",  [SwaggerOperation(Summary = "更新应用配置分类", OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigCategoryService service, [FromBody]global::WoowApp.Services.Core.Dto.UpdateApplicationConfigCategoryInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigCategoryService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var applicationConfigApi = builder.MapGroup(ApiRoot).WithTags("ApplicationConfig");

        applicationConfigApi.MapPost("/ApplicationConfig/add",  [SwaggerOperation(Summary = "增加应用配置", OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [FromBody]global::WoowApp.Services.Core.Dto.AddApplicationConfigInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationConfigApi.MapGet("/ApplicationConfig/ApplicationConfigCategoryTree",  [SwaggerOperation(Summary = "获取分类树列表", OperationId = "ApplicationConfigCategoryTree")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "ApplicationConfigCategoryTreeAsync" });
            var result = await service.ApplicationConfigCategoryTreeAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigApi.MapPost("/ApplicationConfig/delete",  [SwaggerOperation(Summary = "批量删除应用配置", OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [FromBody]global::WoowApp.Services.Core.Dto.BatDeleteApplicationConfigInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationConfigApi.MapDelete("/ApplicationConfig/delete",  [SwaggerOperation(Summary = "删除应用配置", OperationId = "deleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [AsParameters]global::WoowApp.Services.Core.Dto.DeleteApplicationConfigInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationConfigApi.MapPost("/ApplicationConfig/export",  [SwaggerOperation(Summary = "应用配置导出", OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [FromBody]global::WoowApp.Services.Core.Dto.ApplicationConfigInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        applicationConfigApi.MapGet("/ApplicationConfig/detail",  [SwaggerOperation(Summary = "获取应用配置", OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [AsParameters] global::WoowApp.Services.Core.Dto.QueryApplicationConfigInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigApi.MapPost("/ApplicationConfig/import",  [SwaggerOperation(Summary = "应用配置模板导入", OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigApi.MapPost("/ApplicationConfig/importTemplate",  [SwaggerOperation(Summary = "应用配置导入模版下载", OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        applicationConfigApi.MapPost("/ApplicationConfig/page",  [SwaggerOperation(Summary = "分页查询应用配置", OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [FromBody]global::WoowApp.Services.Core.Dto.ApplicationConfigInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationConfigApi.MapPost("/ApplicationConfig/edit",  [SwaggerOperation(Summary = "更新应用配置", OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationConfigService service, [FromBody]global::WoowApp.Services.Core.Dto.UpdateApplicationConfigInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationConfigService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var applicationPrintTemplateManageApi = builder.MapGroup(ApiRoot).WithTags("ApplicationPrintTemplateManage 打印模板管理服务");

        applicationPrintTemplateManageApi.MapPost("/ApplicationPrintTemplateManage/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationPrintTemplateManageService service, [FromBody]global::WoowApp.Services.Core.AddApplicationPrintTemplateManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationPrintTemplateManageService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationPrintTemplateManageApi.MapPost("/ApplicationPrintTemplateManage/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationPrintTemplateManageService service, [FromBody]global::WoowApp.Services.Core.BatDeleteApplicationPrintTemplateManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationPrintTemplateManageService", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationPrintTemplateManageApi.MapDelete("/ApplicationPrintTemplateManage/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationPrintTemplateManageService service, [AsParameters]global::WoowApp.Services.Core.DeleteApplicationPrintTemplateManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationPrintTemplateManageService", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationPrintTemplateManageApi.MapPost("/ApplicationPrintTemplateManage/list",  [SwaggerOperation(OperationId = "list")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationPrintTemplateManageService service, [FromBody]global::WoowApp.Services.Core.ApplicationPrintTemplateManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ApplicationPrintTemplateManageService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationPrintTemplateManageApi.MapPost("/ApplicationPrintTemplateManage/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationPrintTemplateManageService service, [FromBody]global::WoowApp.Services.Core.UpdateApplicationPrintTemplateManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationPrintTemplateManageService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var applicationUserConfigManageApi = builder.MapGroup(ApiRoot).WithTags("ApplicationUserConfigManage 用户配置管理服务");

        applicationUserConfigManageApi.MapPost("/ApplicationUserConfigManage/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationUserConfigManageService service, [FromBody]global::WoowApp.Services.Core.AddApplicationUserConfigManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationUserConfigManageService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationUserConfigManageApi.MapPost("/ApplicationUserConfigManage/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationUserConfigManageService service, [FromBody]global::WoowApp.Services.Core.BatDeleteApplicationUserConfigManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationUserConfigManageService", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        applicationUserConfigManageApi.MapGet("/ApplicationUserConfigManage/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationUserConfigManageService service, [AsParameters] global::WoowApp.Services.Core.QueryApplicationUserConfigManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ApplicationUserConfigManageService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationUserConfigManageApi.MapPost("/ApplicationUserConfigManage/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationUserConfigManageService service, [FromBody]global::WoowApp.Services.Core.ApplicationUserConfigManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ApplicationUserConfigManageService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        applicationUserConfigManageApi.MapPost("/ApplicationUserConfigManage/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.ApplicationUserConfigManageService service, [FromBody]global::WoowApp.Services.Core.UpdateApplicationUserConfigManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ApplicationUserConfigManageService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var authApi = builder.MapGroup(ApiRoot).WithTags("登录授权");

        authApi.MapPost("/Auth/getUserInfo",  [SwaggerOperation(OperationId = "getUserInfo")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.AuthService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "AuthService", MethodName = "GetUserInfo" });
            var result = await service.GetUserInfo();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        authApi.MapGet("/Auth/GetVerificationInfo",  [SwaggerOperation(OperationId = "getVerificationInfo")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.AuthService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "AuthService", MethodName = "GetVerificationInfo" });
            var result = await service.GetVerificationInfo();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        authApi.MapPost("/Auth/loginV2",  [SwaggerOperation(OperationId = "loginV2")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.AuthService service, [FromBody]global::WoowApp.Services.Core.Service.LoginV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "AuthService", MethodName = "LoginV2" });
            var result = await service.LoginV2(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        authApi.MapGet("/Auth/logout",  [SwaggerOperation(OperationId = "logout")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.AuthService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "AuthService", MethodName = "Logout" });
            await service.Logout();
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        authApi.MapPost("/Auth/getRefreshToken",  [SwaggerOperation(OperationId = "getRefreshToken")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.AuthService service, [Required] string accessToken) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "AuthService", MethodName = "RefreshToken" });
            var result = await service.RefreshToken(accessToken);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        authApi.MapPost("/Auth/updateUserInfo",  [SwaggerOperation(OperationId = "updateUserInfo")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.AuthService service, [FromBody]global::WoowApp.Services.Core.Service.UpdateLoginUserInfoInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "AuthService", MethodName = "UpdateLoginUserInfo" });
            await service.UpdateLoginUserInfo(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var constSelectorApi = builder.MapGroup(ApiRoot).WithTags("常量下拉框");

        constSelectorApi.MapGet("/constSelector/allConstSelector",  [SwaggerOperation(OperationId = "allConstSelector")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.ConstSelectorService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ConstSelectorService", MethodName = "GetAllConstSelector" });
            var result = await service.GetAllConstSelector();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        constSelectorApi.MapGet("/constSelector/allConstSelectorWithOptions",  [SwaggerOperation(OperationId = "allConstSelectorWithOptions")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.ConstSelectorService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "ConstSelectorService", MethodName = "GetAllConstSelectorWithOptions" });
            var result = await service.GetAllConstSelectorWithOptions();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        constSelectorApi.MapGet("/constSelector/constSelector",  [SwaggerOperation(OperationId = "constSelector")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.ConstSelectorService service, [FromQuery] string typeName) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "ConstSelectorService", MethodName = "GetConstSelector" });
            var result = await service.GetConstSelector(typeName);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();
      }

      {
        var deviceControlApi = builder.MapGroup(ApiRoot).WithTags("设备控制服务");

        deviceControlApi.MapPost("/DeviceControl/collect",  [SwaggerOperation(Summary = "获取传感器信息（仪表盘）", OperationId = "collect")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlCollectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "DeviceControlService", MethodName = "CollectAsync" });
            var result = await service.CollectAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/getBuzzerState",  [SwaggerOperation(Summary = "获取蜂鸣器状态", OperationId = "getBuzzerState")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlGetBuzzerStateInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "DeviceControlService", MethodName = "GetBuzzerStateAsync" });
            var result = await service.GetBuzzerStateAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/getControlParams",  [SwaggerOperation(Summary = "获取控制参数", OperationId = "getControlParams")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "DeviceControlService", MethodName = "GetControlParamsAsync" });
            var result = await service.GetControlParamsAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/getLampState",  [SwaggerOperation(Summary = "获取灯状态", OperationId = "getLampState")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlGetLampStateInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "DeviceControlService", MethodName = "GetLampStateAsync" });
            var result = await service.GetLampStateAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/getSolenoidValveState",  [SwaggerOperation(Summary = "获取电磁阀状态", OperationId = "getSolenoidValveState")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlGetSolenoidValveStateInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "DeviceControlService", MethodName = "GetSolenoidValveStateAsync" });
            var result = await service.GetSolenoidValveStateAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/setBuzzerState",  [SwaggerOperation(Summary = "设置蜂鸣器状态", OperationId = "setBuzzerState")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlSetBuzzerStateInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "DeviceControlService", MethodName = "SetBuzzerStateAsync" });
            await service.SetBuzzerStateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/setControlParams",  [SwaggerOperation(Summary = "设置控制参数", OperationId = "setControlParams")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlSetControlParamsInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "DeviceControlService", MethodName = "SetControlParamsAsync" });
            await service.SetControlParamsAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/setControlSignal",  [SwaggerOperation(Summary = "设置控制信号", OperationId = "setControlSignal")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlSetControlSignalInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "DeviceControlService", MethodName = "SetControlSignalAsync" });
            await service.SetControlSignalAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/setLampState",  [SwaggerOperation(Summary = "设置灯状态", OperationId = "setLampState")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlSetLampStateInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "DeviceControlService", MethodName = "SetLampStateAsync" });
            await service.SetLampStateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/setPause",  [SwaggerOperation(Summary = "自动采集设置是否暂停", OperationId = "setPause")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]bool pause) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "DeviceControlService", MethodName = "SetPauseAsync" });
            await service.SetPauseAsync(pause);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        deviceControlApi.MapPost("/DeviceControl/setSolenoidValveState",  [SwaggerOperation(Summary = "设置电磁阀状态", OperationId = "setSolenoidValveState")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Application.Services.DeviceControlService service, [FromBody]global::WoowApp.Services.Application.Service.Dto.DeviceControlSetSolenoidValveStateInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "DeviceControlService", MethodName = "SetSolenoidValveStateAsync" });
            await service.SetSolenoidValveStateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var regionApi = builder.MapGroup(ApiRoot).WithTags("Region");

        regionApi.MapPost("/Region/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.AddRegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "RegionService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.BatDeleteRegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "RegionService", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/CompleteInfo",  [SwaggerOperation(OperationId = "CompleteInfo")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.SearchRegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "RegionService", MethodName = "CompleteInfoAsync" });
            var result = await service.CompleteInfoAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        regionApi.MapDelete("/Region/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [AsParameters]global::WoowApp.Services.Core.Service.DeleteRegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "RegionService", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.RegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "RegionService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        regionApi.MapGet("/Region/getAllRegion",  [SwaggerOperation(OperationId = "getAllRegion")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "RegionService", MethodName = "GetAllRegionAsync" });
            var result = await service.GetAllRegionAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.QueryRegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "RegionService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/import",  [SwaggerOperation(OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "RegionService", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/importTemplate",  [SwaggerOperation(OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "RegionService", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.RegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "RegionService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/Search",  [SwaggerOperation(OperationId = "Search")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.SearchRegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "RegionService", MethodName = "SearchAsync" });
            var result = await service.SearchAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        regionApi.MapGet("/Region/SysRegionTree",  [SwaggerOperation(OperationId = "SysRegionTree")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "RegionService", MethodName = "SysRegionTreeAsync" });
            var result = await service.SysRegionTreeAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        regionApi.MapPost("/Region/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.RegionService service, [FromBody]global::WoowApp.Services.Core.Service.UpdateRegionInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "RegionService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysCacheApi = builder.MapGroup(ApiRoot).WithTags("系统缓存");

        sysCacheApi.MapGet("/sysCache/keyList",  [SwaggerOperation(OperationId = "keyList")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Services.ISysCacheService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysCacheService", MethodName = "GetAllCacheKeys" });
            var result = await service.GetAllCacheKeys();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysCacheApi.MapPost("/sysCache/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Services.ISysCacheService service, [FromBody]string cacheKey) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysCacheService", MethodName = "GetStringAsync" });
            var result = await service.GetStringAsync(cacheKey);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysCacheApi.MapPost("/sysCache/remove",  [SwaggerOperation(OperationId = "remove")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Services.ISysCacheService service, [FromBody]string key) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysCacheService", MethodName = "RemoveAsync" });
            await service.RemoveAsync(key);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysCacheApi.MapPost("/sysCache/addString",  [SwaggerOperation(OperationId = "addString")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Services.ISysCacheService service, [FromQuery] string cacheKey, [FromQuery] string value) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysCacheService", MethodName = "SetStringAsync" });
            await service.SetStringAsync(cacheKey, value);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysConfigV2Api = builder.MapGroup(ApiRoot).WithTags("SysConfigV2");

        sysConfigV2Api.MapPost("/SysConfigV2/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [FromBody]global::WoowApp.Services.Core.AddSysConfigV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysConfigV2Api.MapPost("/SysConfigV2/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [FromBody]global::WoowApp.Services.Core.BatDeleteSysConfigV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysConfigV2Api.MapDelete("/SysConfigV2/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [AsParameters]global::WoowApp.Services.Core.DeleteSysConfigV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysConfigV2Api.MapPost("/SysConfigV2/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [FromBody]global::WoowApp.Services.Core.SysConfigV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysConfigV2Api.MapPost("/SysConfigV2/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [FromBody]global::WoowApp.Services.Core.QuerySysConfigV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysConfigV2Api.MapPost("/SysConfigV2/import",  [SwaggerOperation(OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysConfigV2Api.MapPost("/SysConfigV2/importTemplate",  [SwaggerOperation(OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysConfigV2Api.MapPost("/SysConfigV2/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [FromBody]global::WoowApp.Services.Core.SysConfigV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysConfigV2Api.MapPost("/SysConfigV2/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysConfigV2Service service, [FromBody]global::WoowApp.Services.Core.UpdateSysConfigV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysConfigV2Service", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysDictDataApi = builder.MapGroup(ApiRoot).WithTags("系统字典值");

        sysDictDataApi.MapPost("/sysDictData/add",  [SwaggerOperation(OperationId = "add")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.AddDictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictDataService", MethodName = "AddDictData" });
            await service.AddDictData(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysDictDataApi.MapPost("/sysDictData/changeStatus",  [SwaggerOperation(OperationId = "changeStatus")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.ChageStatusDictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictDataService", MethodName = "ChangeDictDataStatus" });
            await service.ChangeDictDataStatus(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysDictDataApi.MapPost("/sysDictData/delete",  [SwaggerOperation(OperationId = "delete")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.DeleteDictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictDataService", MethodName = "DeleteDictData" });
            await service.DeleteDictData(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysDictDataApi.MapPost("/sysDictData/detail",  [SwaggerOperation(OperationId = "detail")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.DictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictDataService", MethodName = "GetDictData" });
            var result = await service.GetDictData(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictDataApi.MapGet("/sysDictData/DictDataDropdown/{code}",  [SwaggerOperation(OperationId = "DictDataDropdown")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromRoute] string code) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysDictDataService", MethodName = "GetDictDataDropdown" });
            var result = await service.GetDictDataDropdown(code);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictDataApi.MapPost("/sysDictData/list",  [SwaggerOperation(OperationId = "list")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.GetDataDictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictDataService", MethodName = "GetDictDataList" });
            var result = await service.GetDictDataList(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictDataApi.MapPost("/sysDictData/pageList",  [SwaggerOperation(OperationId = "pageList")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.PageDictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictDataService", MethodName = "GetDictDataPageList" });
            var result = await service.GetDictDataPageList(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictDataApi.MapPost("/sysDictData/queryDictDataDropdown",  [SwaggerOperation(OperationId = "queryDictDataDropdown")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.QueryDictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysDictDataService", MethodName = "QueryDictDataDropdown" });
            var result = await service.QueryDictDataDropdown(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictDataApi.MapPost("/sysDictData/update",  [SwaggerOperation(OperationId = "update")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictDataService service, [FromBody]global::WoowApp.Services.Core.Service.UpdateDictDataInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictDataService", MethodName = "UpdateDictData" });
            await service.UpdateDictData(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysDictTypeApi = builder.MapGroup(ApiRoot).WithTags("系统字典");

        sysDictTypeApi.MapPost("/sysDictType/add",  [SwaggerOperation(OperationId = "add")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service, [FromBody]global::WoowApp.Services.Core.Service.AddDictTypeInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "AddDictType" });
            await service.AddDictType(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysDictTypeApi.MapPost("/sysDictType/changeStatus",  [SwaggerOperation(OperationId = "changeStatus")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service, [FromBody]global::WoowApp.Services.Core.Service.ChangeStatusDictTypeInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "ChangeDictTypeStatus" });
            await service.ChangeDictTypeStatus(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysDictTypeApi.MapPost("/sysDictType/delete",  [SwaggerOperation(OperationId = "delete")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service, [FromBody]global::WoowApp.Services.Core.Service.DeleteDictTypeInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "DeleteDictType" });
            await service.DeleteDictType(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysDictTypeApi.MapPost("/sysDictType/detail",  [SwaggerOperation(OperationId = "detail")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service, [FromBody]global::WoowApp.Services.Core.Service.DictTypeInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "GetDictType" });
            var result = await service.GetDictType(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictTypeApi.MapPost("/sysDictType/dataList",  [SwaggerOperation(OperationId = "dataList")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service, [FromBody]global::WoowApp.Services.Core.Service.GetDataDictTypeInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "GetDictTypeDataList" });
            var result = await service.GetDictTypeDataList(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictTypeApi.MapGet("/sysDictType/list",  [SwaggerOperation(OperationId = "list")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "GetDictTypeList" });
            var result = await service.GetDictTypeList();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictTypeApi.MapPost("/sysDictType/pageList",  [SwaggerOperation(OperationId = "pageList")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service, [FromBody]global::WoowApp.Services.Core.Service.PageDictTypeInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "GetDictTypePageList" });
            var result = await service.GetDictTypePageList(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysDictTypeApi.MapPost("/sysDictType/update",  [SwaggerOperation(OperationId = "update")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysDictTypeService service, [FromBody]global::WoowApp.Services.Core.Service.UpdateDictTypeInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysDictTypeService", MethodName = "UpdateDictType" });
            await service.UpdateDictType(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysFileApi = builder.MapGroup(ApiRoot).WithTags("文件管理");

        sysFileApi.MapPost("/sysFile/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysFileService service, [FromBody]global::WoowApp.Services.Core.Service.DeleteFileInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysFileService", MethodName = "DeleteFile" });
            await service.DeleteFile(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysFileApi.MapPost("/sysFile/download",  [SwaggerOperation(OperationId = "download")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysFileService service, [FromBody]global::WoowApp.Services.Core.Service.FileInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysFileService", MethodName = "DownloadFile" });
            var result = await service.DownloadFile(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysFileApi.MapGet("/sysFile/list",  [SwaggerOperation(OperationId = "list")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysFileService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysFileService", MethodName = "GetFileList" });
            var result = await service.GetFileList();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysFileApi.MapPost("/sysFile/pageList",  [SwaggerOperation(OperationId = "pageList")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysFileService service, [FromBody]global::WoowApp.Services.Core.Service.PageFileInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysFileService", MethodName = "QueryFilePageList" });
            var result = await service.QueryFilePageList(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysFileApi.MapPost("/sysFile/upload",  [SwaggerOperation(OperationId = "upload")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.Service.SysFileService service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysFileService", MethodName = "UploadFile" });
            var result = await service.UploadFile(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();
      }

      {
        var sysFileV2Api = builder.MapGroup(ApiRoot).WithTags("SysFileV2");

        sysFileV2Api.MapGet("/sysFileV2/download/{id}",  [SwaggerOperation(OperationId = "download")] [AllowAnonymous] async ([FromServices] global::WoowApp.Services.Core.SysFileV2Service service, [FromRoute] string id, [FromQuery] int? download) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysFileV2Service", MethodName = "DownloadFile" });
            var result = await service.DownloadFile(id, download);
            return TypedResults.File(result.FileStream, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysFileV2Api.MapPost("/SysFileV2/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysFileV2Service service, [FromBody]global::WoowApp.Services.Core.SysFileV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysFileV2Service", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysFileV2Api.MapPost("/SysFileV2/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysFileV2Service service, [FromBody]global::WoowApp.Services.Core.QuerySysFileV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysFileV2Service", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysFileV2Api.MapPost("/SysFileV2/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysFileV2Service service, [FromBody]global::WoowApp.Services.Core.SysFileV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysFileV2Service", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();
      }

      {
        var sysLogExApi = builder.MapGroup(ApiRoot).WithTags("SysLogEx");

        sysLogExApi.MapPost("/SysLogEx/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysLogExService service, [FromBody]global::WoowApp.Services.Core.SysLogExInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysLogExService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysLogExApi.MapPost("/SysLogEx/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysLogExService service, [FromBody]global::WoowApp.Services.Core.QuerySysLogExInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysLogExService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysLogExApi.MapPost("/SysLogEx/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysLogExService service, [FromBody]global::WoowApp.Services.Core.SysLogExInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysLogExService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();
      }

      {
        var sysLogOpApi = builder.MapGroup(ApiRoot).WithTags("SysLogOp");

        sysLogOpApi.MapPost("/SysLogOp/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysLogOpService service, [FromBody]global::WoowApp.Services.Core.SysLogOpInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysLogOpService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysLogOpApi.MapPost("/SysLogOp/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysLogOpService service, [FromBody]global::WoowApp.Services.Core.QuerySysLogOpInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysLogOpService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysLogOpApi.MapPost("/SysLogOp/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysLogOpService service, [FromBody]global::WoowApp.Services.Core.SysLogOpInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysLogOpService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();
      }

      {
        var sysMenuV2Api = builder.MapGroup(ApiRoot).WithTags("SysMenuV2");

        sysMenuV2Api.MapPost("/SysMenuV2/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMenuV2Service service, [FromBody]global::WoowApp.Services.Core.AddSysMenuV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMenuV2Service", MethodName = "AddAsync" });
            var result = await service.AddAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysMenuV2Api.MapPost("/SysMenuV2/batDelete",  [SwaggerOperation(OperationId = "Delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMenuV2Service service, [FromBody]global::WoowApp.Services.Core.BatDeleteSysMenuV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMenuV2Service", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysMenuV2Api.MapDelete("/SysMenuV2/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMenuV2Service service, [AsParameters]global::WoowApp.Services.Core.DeleteSysMenuV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMenuV2Service", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysMenuV2Api.MapPost("/SysMenuV2/list",  [SwaggerOperation(OperationId = "list")] [Authorize] async ([FromServices] global::WoowApp.Services.Core.SysMenuV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysMenuV2Service", MethodName = "GetList" });
            var result = await service.GetList();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysMenuV2Api.MapPost("/SysMenuV2/getMenuListV2",  [SwaggerOperation(OperationId = "getMenuListV2")] [Authorize] async ([FromServices] global::WoowApp.Services.Core.SysMenuV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysMenuV2Service", MethodName = "GetLoginMenuTree" });
            var result = await service.GetLoginMenuTree();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysMenuV2Api.MapPost("/SysMenuV2/nodeDrop",  [SwaggerOperation(OperationId = "nodeDrop")] [Authorize] async ([FromServices] global::WoowApp.Services.Core.SysMenuV2Service service, [FromBody]global::WoowApp.Services.Core.SysMenuV2NodeDropInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMenuV2Service", MethodName = "NodeDrop" });
            await service.NodeDrop(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysMenuV2Api.MapPost("/SysMenuV2/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMenuV2Service service, [FromBody]global::WoowApp.Services.Core.UpdateSysMenuV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMenuV2Service", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysMessageApi = builder.MapGroup(ApiRoot).WithTags("SysMessage");

        sysMessageApi.MapPost("/SysMessage/add",  [SwaggerOperation(Summary = "增加系统消息", OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service, [FromBody]global::WoowApp.Services.Core.Dto.AddSysMessageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMessageService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysMessageApi.MapPost("/SysMessage/batHandle",  [SwaggerOperation(Summary = "批量处理系统消息", OperationId = "batHandle")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service, [FromBody]global::WoowApp.Services.Core.Dto.BatHandleSysMessageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMessageService", MethodName = "BatHandleAsync" });
            await service.BatHandleAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysMessageApi.MapPost("/SysMessage/export",  [SwaggerOperation(Summary = "系统消息导出", OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service, [FromBody]global::WoowApp.Services.Core.Dto.SysMessageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMessageService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysMessageApi.MapGet("/SysMessage/detail",  [SwaggerOperation(Summary = "获取系统消息", OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service, [AsParameters] global::WoowApp.Services.Core.Dto.QuerySysMessageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysMessageService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysMessageApi.MapPost("/SysMessage/import",  [SwaggerOperation(Summary = "系统消息模板导入", OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMessageService", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysMessageApi.MapPost("/SysMessage/importTemplate",  [SwaggerOperation(Summary = "系统消息导入模版下载", OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMessageService", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysMessageApi.MapPost("/SysMessage/page",  [SwaggerOperation(Summary = "分页查询系统消息", OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service, [FromBody]global::WoowApp.Services.Core.Dto.SysMessageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysMessageService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysMessageApi.MapPost("/SysMessage/edit",  [SwaggerOperation(Summary = "更新系统消息", OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysMessageService service, [FromBody]global::WoowApp.Services.Core.Dto.UpdateSysMessageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysMessageService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysOrgV2Api = builder.MapGroup(ApiRoot).WithTags("SysOrgV2");

        sysOrgV2Api.MapPost("/SysOrgV2/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [FromBody]global::WoowApp.Services.Core.AddSysOrgV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysOrgV2Api.MapPost("/SysOrgV2/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [FromBody]global::WoowApp.Services.Core.BatDeleteSysOrgV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysOrgV2Api.MapDelete("/SysOrgV2/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [AsParameters]global::WoowApp.Services.Core.DeleteSysOrgV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysOrgV2Api.MapPost("/SysOrgV2/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [FromBody]global::WoowApp.Services.Core.SysOrgV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysOrgV2Api.MapPost("/SysOrgV2/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [FromBody]global::WoowApp.Services.Core.QuerySysOrgV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysOrgV2Api.MapPost("/SysOrgV2/import",  [SwaggerOperation(OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysOrgV2Api.MapPost("/SysOrgV2/importTemplate",  [SwaggerOperation(OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysOrgV2Api.MapPost("/SysOrgV2/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [FromBody]global::WoowApp.Services.Core.SysOrgV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysOrgV2Api.MapGet("/SysOrgV2/SysOrgTree",  [SwaggerOperation(OperationId = "SysOrgTree")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "SysOrgTreeAsync" });
            var result = await service.SysOrgTreeAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysOrgV2Api.MapPost("/SysOrgV2/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysOrgV2Service service, [FromBody]global::WoowApp.Services.Core.UpdateSysOrgV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysOrgV2Service", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysPosV2Api = builder.MapGroup(ApiRoot).WithTags("SysPosV2");

        sysPosV2Api.MapPost("/SysPosV2/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [FromBody]global::WoowApp.Services.Core.AddSysPosV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysPosV2Api.MapPost("/SysPosV2/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [FromBody]global::WoowApp.Services.Core.BatDeleteSysPosV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysPosV2Api.MapDelete("/SysPosV2/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [AsParameters]global::WoowApp.Services.Core.DeleteSysPosV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysPosV2Api.MapPost("/SysPosV2/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [FromBody]global::WoowApp.Services.Core.SysPosV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysPosV2Api.MapPost("/SysPosV2/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [FromBody]global::WoowApp.Services.Core.QuerySysPosV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysPosV2Api.MapPost("/SysPosV2/import",  [SwaggerOperation(OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysPosV2Api.MapPost("/SysPosV2/importTemplate",  [SwaggerOperation(OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysPosV2Api.MapPost("/SysPosV2/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [FromBody]global::WoowApp.Services.Core.SysPosV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysPosV2Api.MapPost("/SysPosV2/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysPosV2Service service, [FromBody]global::WoowApp.Services.Core.UpdateSysPosV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysPosV2Service", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysRoleApi = builder.MapGroup(ApiRoot).WithTags("系统角色");

        sysRoleApi.MapPost("/sysRole/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.AddRoleInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "AddRole" });
            await service.AddRole(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.DeleteRoleInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "DeleteRole" });
            await service.DeleteRole(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleApi.MapGet("/sysRole/list",  [SwaggerOperation(OperationId = "list")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "GetRoleList" });
            var result = await service.GetRoleList();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/ownMenu",  [SwaggerOperation(OperationId = "ownMenu")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.RoleInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "GetRoleOwnMenu" });
            var result = await service.GetRoleOwnMenu(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/ownOrg",  [SwaggerOperation(OperationId = "ownOrg")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.RoleInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "GetRoleOwnOrg" });
            var result = await service.GetRoleOwnOrg(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/pageList",  [SwaggerOperation(OperationId = "pageList")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.PageRoleInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "GetRolePageList" });
            var result = await service.GetRolePageList(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/grantData",  [SwaggerOperation(OperationId = "grantData")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.RoleOrgInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "GrantRoleDataScope" });
            await service.GrantRoleDataScope(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/grantMenu",  [SwaggerOperation(OperationId = "grantMenu")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.RoleMenuInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "GrantRoleMenu" });
            await service.GrantRoleMenu(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/setStatus",  [SwaggerOperation(OperationId = "setStatus")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.RoleInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "SetRoleStatus" });
            var result = await service.SetRoleStatus(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleApi.MapPost("/sysRole/update",  [SwaggerOperation(OperationId = "update")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.Service.SysRoleService service, [FromBody]global::WoowApp.Services.Core.Service.UpdateRoleInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleService", MethodName = "UpdateRole" });
            await service.UpdateRole(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysRoleV2Api = builder.MapGroup(ApiRoot).WithTags("SysRoleV2");

        sysRoleV2Api.MapPost("/SysRoleV2/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.AddSysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.BatDeleteSysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleV2Api.MapDelete("/SysRoleV2/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [AsParameters]global::WoowApp.Services.Core.DeleteSysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.SysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.QuerySysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/grantMenu",  [SwaggerOperation(OperationId = "grantMenu")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.Service.RoleMenuInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "GrantRoleMenu" });
            await service.GrantRoleMenu(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/import",  [SwaggerOperation(OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/importTemplate",  [SwaggerOperation(OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/ownMenu",  [SwaggerOperation(OperationId = "ownMenu")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.OwnMenuSysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "OwnMenuAsync" });
            var result = await service.OwnMenuAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.SysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/setStatus",  [SwaggerOperation(OperationId = "setStatus")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.SetSysRoleStatusInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "SetUserStatus" });
            var result = await service.SetUserStatus(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleV2Api.MapGet("/SysRoleV2/SysMenuTree",  [SwaggerOperation(OperationId = "SysMenuTree")] [Authorize] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "SysMenuTreeAsync" });
            var result = await service.SysMenuTreeAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysRoleV2Api.MapPost("/SysRoleV2/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysRoleV2Service service, [FromBody]global::WoowApp.Services.Core.UpdateSysRoleV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysRoleV2Service", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysUserGroupManageApi = builder.MapGroup(ApiRoot).WithTags("SysUserGroupManage");

        sysUserGroupManageApi.MapPost("/SysUserGroupManage/add",  [SwaggerOperation(Summary = "增加用户组管理", OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service, [FromBody]global::WoowApp.Services.Core.Dto.AddSysUserGroupManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysUserGroupManageApi.MapPost("/SysUserGroupManage/delete",  [SwaggerOperation(Summary = "批量删除用户组管理", OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service, [FromBody]global::WoowApp.Services.Core.Dto.BatDeleteSysUserGroupManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysUserGroupManageApi.MapPost("/SysUserGroupManage/export",  [SwaggerOperation(Summary = "用户组管理导出", OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserGroupManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserGroupManageApi.MapGet("/SysUserGroupManage/detail",  [SwaggerOperation(Summary = "获取用户组管理", OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service, [AsParameters] global::WoowApp.Services.Core.Dto.QuerySysUserGroupManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupManageApi.MapPost("/SysUserGroupManage/import",  [SwaggerOperation(Summary = "用户组管理模板导入", OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupManageApi.MapPost("/SysUserGroupManage/importTemplate",  [SwaggerOperation(Summary = "用户组管理导入模版下载", OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserGroupManageApi.MapPost("/SysUserGroupManage/page",  [SwaggerOperation(Summary = "分页查询用户组管理", OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserGroupManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupManageApi.MapPost("/SysUserGroupManage/edit",  [SwaggerOperation(Summary = "更新用户组管理", OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupManageService service, [FromBody]global::WoowApp.Services.Core.Dto.UpdateSysUserGroupManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupManageService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysUserGroupMemberManageApi = builder.MapGroup(ApiRoot).WithTags("SysUserGroupMemberManage");

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/add",  [SwaggerOperation(Summary = "增加用户组成员管理", OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [FromBody]global::WoowApp.Services.Core.Dto.AddSysUserGroupMemberManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/delete",  [SwaggerOperation(Summary = "批量删除用户组成员管理", OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [FromBody]global::WoowApp.Services.Core.Dto.BatDeleteSysUserGroupMemberManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/export",  [SwaggerOperation(Summary = "用户组成员管理导出", OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserGroupMemberManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapGet("/SysUserGroupMemberManage/detail",  [SwaggerOperation(Summary = "获取用户组成员管理", OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [AsParameters] global::WoowApp.Services.Core.Dto.QuerySysUserGroupMemberManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/import",  [SwaggerOperation(Summary = "用户组成员管理模板导入", OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/importTemplate",  [SwaggerOperation(Summary = "用户组成员管理导入模版下载", OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/page",  [SwaggerOperation(Summary = "分页查询用户组成员管理", OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserGroupMemberManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/SysUserDropdown",  [SwaggerOperation(Summary = "获取用户列表", OperationId = "SysUserDropdown")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [FromBody]global::WoowApp.Services.Core.Base.BaseApiSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "SysUserDropdownAsync" });
            var result = await service.SysUserDropdownAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/SysUserGroupDropdown",  [SwaggerOperation(Summary = "获取用户组列表", OperationId = "SysUserGroupDropdown")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [FromBody]global::WoowApp.Services.Core.Base.BaseApiSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "SysUserGroupDropdownAsync" });
            var result = await service.SysUserGroupDropdownAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupMemberManageApi.MapPost("/SysUserGroupMemberManage/edit",  [SwaggerOperation(Summary = "更新用户组成员管理", OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupMemberManageService service, [FromBody]global::WoowApp.Services.Core.Dto.UpdateSysUserGroupMemberManageInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupMemberManageService", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

      {
        var sysUserGroupSelectApi = builder.MapGroup(ApiRoot).WithTags("SysUserGroupSelect");

        sysUserGroupSelectApi.MapPost("/SysUserGroupSelect/export",  [SwaggerOperation(Summary = "选择用户组导出", OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupSelectService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserGroupSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserGroupSelectService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserGroupSelectApi.MapGet("/SysUserGroupSelect/detail",  [SwaggerOperation(Summary = "获取选择用户组", OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupSelectService service, [AsParameters] global::WoowApp.Services.Core.Dto.QuerySysUserGroupSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupSelectService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserGroupSelectApi.MapPost("/SysUserGroupSelect/page",  [SwaggerOperation(Summary = "分页查询选择用户组", OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserGroupSelectService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserGroupSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserGroupSelectService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();
      }

      {
        var sysUserSelectApi = builder.MapGroup(ApiRoot).WithTags("SysUserSelect");

        sysUserSelectApi.MapPost("/SysUserSelect/export",  [SwaggerOperation(Summary = "选择用户导出", OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserSelectService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserSelectService", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserSelectApi.MapGet("/SysUserSelect/detail",  [SwaggerOperation(Summary = "获取选择用户", OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserSelectService service, [AsParameters] global::WoowApp.Services.Core.Dto.QuerySysUserSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserSelectService", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserSelectApi.MapPost("/SysUserSelect/page",  [SwaggerOperation(Summary = "分页查询选择用户", OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserSelectService service, [FromBody]global::WoowApp.Services.Core.Dto.SysUserSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserSelectService", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserSelectApi.MapGet("/SysUserSelect/SysOrgTree",  [SwaggerOperation(Summary = "获取机构Id树列表", OperationId = "SysOrgTree")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserSelectService service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserSelectService", MethodName = "SysOrgTreeAsync" });
            var result = await service.SysOrgTreeAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserSelectApi.MapPost("/SysUserSelect/SysPosDropdown",  [SwaggerOperation(Summary = "获取职位Id列表", OperationId = "SysPosDropdown")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserSelectService service, [FromBody]global::WoowApp.Services.Core.Base.BaseApiSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserSelectService", MethodName = "SysPosDropdownAsync" });
            var result = await service.SysPosDropdownAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();
      }

      {
        var sysUserV2Api = builder.MapGroup(ApiRoot).WithTags("SysUserV2");

        sysUserV2Api.MapPost("/SysUserV2/add",  [SwaggerOperation(OperationId = "add")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.AddSysUserV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "AddAsync" });
            await service.AddAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/delete",  [SwaggerOperation(OperationId = "delete")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.BatDeleteSysUserV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "BatDeleteAsync" });
            await service.BatDeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/changeUserPwd",  [SwaggerOperation(OperationId = "changeUserPwd")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.Service.ChangePwdInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "ChangeUserPwd" });
            var result = await service.ChangeUserPwd(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapDelete("/SysUserV2/delete",  [SwaggerOperation(OperationId = "DeleteOne")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [AsParameters]global::WoowApp.Services.Core.DeleteSysUserV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "DeleteAsync" });
            await service.DeleteAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/export",  [SwaggerOperation(OperationId = "export")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.SysUserV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "ExportAsync" });
            var result = await service.ExportAsync(input);
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/detail",  [SwaggerOperation(OperationId = "detail")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.QuerySysUserV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "GetAsync" });
            var result = await service.GetAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/import",  [SwaggerOperation(OperationId = "import")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [Required] global::Microsoft.AspNetCore.Http.IFormFile file) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "ImportAsync" });
            var result = await service.ImportAsync(file);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/importTemplate",  [SwaggerOperation(OperationId = "importTemplate")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "ImportTemplateAsync" });
            var result = await service.ImportTemplateAsync();
            return TypedResults.File(result.FileContents, result.ContentType, result.FileDownloadName);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/page",  [SwaggerOperation(OperationId = "page")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.SysUserV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = true, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "PageAsync" });
            var result = await service.PageAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/resetPwd",  [SwaggerOperation(OperationId = "resetPwd")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.Service.ResetPwdUserInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "ResetUserPwd" });
            var result = await service.ResetUserPwd(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/setStatus",  [SwaggerOperation(OperationId = "setStatus")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.SetSysUserStatusInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "SetUserStatus" });
            var result = await service.SetUserStatus(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapGet("/SysUserV2/SysOrgTree",  [SwaggerOperation(OperationId = "SysOrgTree")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service ) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "SysOrgTreeAsync" });
            var result = await service.SysOrgTreeAsync();
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/SysPosDropdown",  [SwaggerOperation(OperationId = "SysPosDropdown")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.Base.BaseApiSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "SysPosDropdownAsync" });
            var result = await service.SysPosDropdownAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/SysRoleDropdown",  [SwaggerOperation(OperationId = "SysRoleDropdown")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.Base.BaseApiSelectInput input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "SysRoleDropdownAsync" });
            var result = await service.SysRoleDropdownAsync(input);
            return ResultsExtensions.Ok(result);
         }).DisableAntiforgery();

        sysUserV2Api.MapPost("/SysUserV2/edit",  [SwaggerOperation(OperationId = "edit")] [Authorize("WoowApi")] async ([FromServices] global::WoowApp.Services.Core.SysUserV2Service service, [FromBody]global::WoowApp.Services.Core.UpdateSysUserV2Input input) =>
         {
            Woow.SetApiEnv(new() { NotLog = false, NonUnify = false, ClassName = "SysUserV2Service", MethodName = "UpdateAsync" });
            await service.UpdateAsync(input);
            return ResultsExtensions.Ok();
         }).DisableAntiforgery();
      }

         WoowApp.Services.Core.Filters.LogOperateHandler.SetLogPathList([
"/applicationconfigcategory/add",
"/applicationconfigcategory/applicationconfigcategorytree",
"/applicationconfigcategory/delete",
"/applicationconfigcategory/delete",
"/applicationconfigcategory/export",
"/applicationconfigcategory/import",
"/applicationconfigcategory/importtemplate",
"/applicationconfigcategory/edit",
"/applicationconfig/add",
"/applicationconfig/applicationconfigcategorytree",
"/applicationconfig/delete",
"/applicationconfig/delete",
"/applicationconfig/export",
"/applicationconfig/import",
"/applicationconfig/importtemplate",
"/applicationconfig/edit",
"/applicationprinttemplatemanage/add",
"/applicationprinttemplatemanage/delete",
"/applicationprinttemplatemanage/delete",
"/applicationprinttemplatemanage/edit",
"/applicationuserconfigmanage/add",
"/applicationuserconfigmanage/delete",
"/applicationuserconfigmanage/edit",
"/auth/getuserinfo",
"/auth/loginv2",
"/auth/logout",
"/auth/getrefreshtoken",
"/auth/updateuserinfo",
"/constselector/allconstselector",
"/constselector/constselector",
"/devicecontrol/setbuzzerstate",
"/devicecontrol/setcontrolparams",
"/devicecontrol/setcontrolsignal",
"/devicecontrol/setlampstate",
"/devicecontrol/setsolenoidvalvestate",
"/region/add",
"/region/delete",
"/region/delete",
"/region/export",
"/region/import",
"/region/importtemplate",
"/region/edit",
"/syscache/keylist",
"/syscache/detail",
"/syscache/remove",
"/syscache/addstring",
"/sysconfigv2/add",
"/sysconfigv2/delete",
"/sysconfigv2/delete",
"/sysconfigv2/export",
"/sysconfigv2/import",
"/sysconfigv2/importtemplate",
"/sysconfigv2/edit",
"/sysdictdata/add",
"/sysdictdata/changestatus",
"/sysdictdata/delete",
"/sysdictdata/detail",
"/sysdictdata/list",
"/sysdictdata/pagelist",
"/sysdictdata/update",
"/sysdicttype/add",
"/sysdicttype/changestatus",
"/sysdicttype/delete",
"/sysdicttype/detail",
"/sysdicttype/datalist",
"/sysdicttype/list",
"/sysdicttype/pagelist",
"/sysdicttype/update",
"/sysfile/delete",
"/sysfile/download",
"/sysfile/list",
"/sysfile/pagelist",
"/sysfile/upload",
"/sysfilev2/export",
"/syslogex/export",
"/syslogop/export",
"/sysmenuv2/add",
"/sysmenuv2/batdelete",
"/sysmenuv2/delete",
"/sysmenuv2/nodedrop",
"/sysmenuv2/edit",
"/sysmessage/add",
"/sysmessage/bathandle",
"/sysmessage/export",
"/sysmessage/import",
"/sysmessage/importtemplate",
"/sysmessage/edit",
"/sysorgv2/add",
"/sysorgv2/delete",
"/sysorgv2/delete",
"/sysorgv2/export",
"/sysorgv2/import",
"/sysorgv2/importtemplate",
"/sysorgv2/sysorgtree",
"/sysorgv2/edit",
"/sysposv2/add",
"/sysposv2/delete",
"/sysposv2/delete",
"/sysposv2/export",
"/sysposv2/import",
"/sysposv2/importtemplate",
"/sysposv2/edit",
"/sysrole/add",
"/sysrole/delete",
"/sysrole/list",
"/sysrole/ownmenu",
"/sysrole/ownorg",
"/sysrole/pagelist",
"/sysrole/grantdata",
"/sysrole/grantmenu",
"/sysrole/setstatus",
"/sysrole/update",
"/sysrolev2/add",
"/sysrolev2/delete",
"/sysrolev2/delete",
"/sysrolev2/export",
"/sysrolev2/grantmenu",
"/sysrolev2/import",
"/sysrolev2/importtemplate",
"/sysrolev2/setstatus",
"/sysrolev2/edit",
"/sysusergroupmanage/add",
"/sysusergroupmanage/delete",
"/sysusergroupmanage/export",
"/sysusergroupmanage/import",
"/sysusergroupmanage/importtemplate",
"/sysusergroupmanage/edit",
"/sysusergroupmembermanage/add",
"/sysusergroupmembermanage/delete",
"/sysusergroupmembermanage/export",
"/sysusergroupmembermanage/import",
"/sysusergroupmembermanage/importtemplate",
"/sysusergroupmembermanage/edit",
"/sysusergroupselect/export",
"/sysuserselect/export",
"/sysuserv2/add",
"/sysuserv2/delete",
"/sysuserv2/changeuserpwd",
"/sysuserv2/delete",
"/sysuserv2/export",
"/sysuserv2/import",
"/sysuserv2/importtemplate",
"/sysuserv2/resetpwd",
"/sysuserv2/setstatus",
"/sysuserv2/sysorgtree",
"/sysuserv2/sysposdropdown",
"/sysuserv2/sysroledropdown",
"/sysuserv2/edit"
         ]);
    }
}
