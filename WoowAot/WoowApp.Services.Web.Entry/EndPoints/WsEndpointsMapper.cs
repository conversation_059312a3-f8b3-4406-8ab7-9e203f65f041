using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using System.Net.WebSockets;
using WoowApp.Services.Application;
using WoowApp.Services.Core;

namespace WoowApp.Services.Web.Entry.EndPoints;

/// <summary>
/// WebSocket入口绑定
/// </summary>
public static class WsEndpointsMapper
{
    /// <summary>
    /// 绑定WebSocket服务
    /// </summary>
    /// <param name="app"></param>
    public static void MapWs(this IEndpointRouteBuilder endpoints)
    {
        endpoints.Map("ws/GetLogs", async (HttpContext context, [FromServices] WsDataService wsDataService, [FromServices] WsChartService wsLogService) =>
        {
            if (context.WebSockets.IsWebSocketRequest)
            {
                using var webSocket = await wsDataService.AcceptWebSocketAsync();
                var key = context.Request.Query["key"].ToString();
                var data = wsLogService.GetLogs(key);
                await wsDataService.SendDataAsync(data);
            }
            else
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
            }
        });
        //endpoints.Map("ws/GetOperatorStationStatus", async (HttpContext context, [FromServices] WsDataService wsDataService, [FromServices] WsChartService wsLogService) =>
        //{
        //    if (context.WebSockets.IsWebSocketRequest)
        //    {
        //        using var webSocket = await wsDataService.AcceptWebSocketAsync();
        //        var key = context.Request.Query["operatorStationId"].ToString();
        //        var data = wsLogService.GetOperatorStationStatus(key);
        //        await wsDataService.SendDataAsync(data);
        //    }
        //    else
        //    {
        //        context.Response.StatusCode = StatusCodes.Status400BadRequest;
        //    }
        //});

        endpoints.Map("ws/client", async (HttpContext context, [FromServices] WsClientService wsClientService) =>
        {
            if (context.WebSockets.IsWebSocketRequest)
            {
                using var webSocket = await wsClientService.AcceptWebSocketAsync();
                await wsClientService.HandleWebSocket();
            }
            else
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
            }
        });


    }
}
