using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using WoowApp.Services.CodeGen.Entry.Filters;
using WoowApp.Services.Core.Base;
using WoowApp.Services.Shared.Base;
using WoowApp.Services.Web.Entry.Filters;

namespace WoowApp.Services.Web.Entry.EndPoints;

/// <summary>
/// 动态Api绑定
/// </summary>
public static partial class EndpointsMapper
{
    /// <summary>
    /// 映射所有接口
    /// </summary>
    /// <param name="app"></param>
    public static void MapEndpoints(this IEndpointRouteBuilder app)
    {
        var root = app.MapGroup("").AddEndpointFilterFactory(GlobalValidationFilter.Validate);

        // 这里调用各个具体的 endpoint 映射方法
        root.MapGeneratorEndpoints();
    }

    /// <summary>
    /// Api前缀
    /// </summary>
    public static string ApiRoot { get; set; } = string.Empty;

    /// <summary>
    /// 自动生成 endpoint 方法
    /// </summary>
    /// <param name="app"></param>
    static partial void MapGeneratorEndpoints(this IEndpointRouteBuilder app);

    
}