using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace WoowApp.Services.Application.Entities;

/// <summary>
/// 工位传感器设置
/// </summary>
//[SugarTable("valve_operator_station_sensor", "工位传感器设置")]
[Description("工位传感器设置")]
[Display(Name = "工位传感器设置")]
public partial class ValveOperatorStationSensor  : EntityBase
{
    /// <summary>
    /// 名称
    /// </summary>
    [Display(Name = "名称")]
    [Required(ErrorMessage = "{0} - 不能为空"), MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "名称", Length = 50)]
    public string SensorName { get; set; }

    /// <summary>
    /// 序号@@从1开始
    /// </summary>
    [Display(Name = "序号", Description = "从1开始")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SugarColumn(ColumnDescription = "序号@@从1开始")]
    public int SerialNumber { get; set; }

    /// <summary>
    /// 传感器大类
    /// </summary>
    [Display(Name = "传感器大类")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SugarColumn(ColumnDescription = "传感器大类")]
    public SensorTypeEnum SensorType { get; set; }

    /// <summary>
    /// 模数转换方法
    /// </summary>
    [Display(Name = "模数转换方法")]
    [SugarColumn(ColumnDescription = "模数转换方法")]
    public SensorConvertTypeEnum? SensorConvertType { get; set; }

    /// <summary>
    /// 测试值单位
    /// </summary>
    [Display(Name = "测试值单位")]
    [MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "测试值单位", Length = 50)]
    public string MeasureUnit { get; set; }

    /// <summary>
    /// 测试值最小值
    /// </summary>
    [Display(Name = "测试值最小值")]
    [SugarColumn(ColumnDescription = "测试值最小值", Length = 18, DecimalDigits = 6)]
    public decimal? MeasureMin { get; set; }

    /// <summary>
    /// 测试值最大值
    /// </summary>
    [Display(Name = "测试值最大值")]
    [SugarColumn(ColumnDescription = "测试值最大值", Length = 18, DecimalDigits = 6)]
    public decimal? MeasureMax { get; set; }

    /// <summary>
    /// 模拟量单位
    /// </summary>
    [Display(Name = "模拟量单位")]
    [MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "模拟量单位", Length = 50)]
    public string AnalogUnit { get; set; }

    /// <summary>
    /// 模拟信号最小值
    /// </summary>
    [Display(Name = "模拟信号最小值")]
    [SugarColumn(ColumnDescription = "模拟信号最小值", Length = 18, DecimalDigits = 6)]
    public decimal? AnalogMin { get; set; }

    /// <summary>
    /// 模拟信号最大值
    /// </summary>
    [Display(Name = "模拟信号最大值")]
    [SugarColumn(ColumnDescription = "模拟信号最大值", Length = 18, DecimalDigits = 6)]
    public decimal? AnalogMax { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [Display(Name = "排序")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SugarColumn(ColumnDescription = "排序")]
    public int SortId { get; set; }

    /// <summary>
    /// 反转
    /// </summary>
    [Display(Name = "反转")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SugarColumn(ColumnDescription = "反转")]
    public bool Inversion { get; set; }

    /// <summary>
    /// 硬件版本
    /// </summary>
    [Display(Name = "硬件版本")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SugarColumn(ColumnDescription = "硬件版本")]
    public HardwareVersionEnum HardwareVersion { get; set; }

    /// <summary>
    /// 工位
    /// </summary>
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SugarColumn(ColumnDescription = "工位")]
    public long ValveOperatorStationId { get; set; }


}