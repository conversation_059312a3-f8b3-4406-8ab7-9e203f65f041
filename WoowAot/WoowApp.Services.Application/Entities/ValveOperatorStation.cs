using System;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace WoowApp.Services.Application.Entities;

/// <summary>
/// 操作工位
/// </summary>
//[SugarTable("valve_operator_station", "操作工位")]
[Description("操作工位")]
[Display(Name = "操作工位")]
public partial class ValveOperatorStation  : EntityBase
{
    /// <summary>
    /// 名称
    /// </summary>
    [Display(Name = "名称")]
    [Required(ErrorMessage = "{0} - 不能为空"), MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "名称", Length = 50)]
    public string Name { get; set; }

    /// <summary>
    /// 客户端Key
    /// </summary>
    [Display(Name = "客户端Key")]
    [Required(ErrorMessage = "{0} - 不能为空"), MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "客户端Key", Length = 50)]
    public string AppKey { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    [Display(Name = "密码")]
    [Required(ErrorMessage = "{0} - 不能为空"), MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "密码", Length = 50)]
    public string Password { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [Display(Name = "描述")]
    [MaxLength(200, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "描述", Length = 200)]
    public string Description { get; set; }

    /// <summary>
    /// 温度
    /// </summary>
    [Display(Name = "温度")]
    [MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "温度", Length = 50)]
    public string Temperature { get; set; }

    /// <summary>
    /// 湿度
    /// </summary>
    [Display(Name = "湿度")]
    [MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "湿度", Length = 50)]
    public string Humidity { get; set; }

    /// <summary>
    /// 天气
    /// </summary>
    [Display(Name = "天气")]
    [MaxLength(50, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "天气", Length = 50)]
    public string Weather { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [Display(Name = "地址")]
    [MaxLength(100, ErrorMessage = "{0}不能超过{1}个字符")]
    [SugarColumn(ColumnDescription = "地址", Length = 100)]
    public string Address { get; set; }

    /// <summary>
    /// 距离测量类型
    /// </summary>
    [Display(Name = "距离测量类型")]
    [SugarColumn(ColumnDescription = "距离测量类型")]
    public DistanceMeasurementTypeEnum? DistanceMeasurementType { get; set; }

    /// <summary>
    /// 硬件版本
    /// </summary>
    [Display(Name = "硬件版本")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SugarColumn(ColumnDescription = "硬件版本")]
    public HardwareVersionEnum HardwareVersion { get; set; }


}