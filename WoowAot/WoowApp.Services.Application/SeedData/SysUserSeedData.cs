namespace WoowApp.Services.Application;

/// <summary>
/// 系统用户表种子数据
/// </summary>
public partial class SysUserSeedData : ISqlSugarEntitySeedData<SysUser>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysUser> HasData()
    {
        return new[]
        {
            new SysUser{ Id = 252885263003721, UserName = "admin", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "管理员", Avatar = "", Birthday = DateTime.Parse("1987/1/1 0:00:00"), Sex = (GenderEnum)1, Email = "", Phone = "admin", RealName = "管理员", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)1, Remark = "管理员", Status = (StatusEnum)2, OrgId = 394281300517189, PosId = 252885263003720, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            //new SysUser{ Id = 252885263003722, UserName = "guest", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "普通用户", Avatar = "", Birthday = DateTime.Parse("1987/1/1 0:00:00"), Sex = (GenderEnum)2, Email = "", Phone = "guest", RealName = "普通用户", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "普通账号", Status = (StatusEnum)2, OrgId = 394281300517189, PosId = 252885263003721, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            //new SysUser{ Id = ***************, UserName = "李筱凡", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "", Avatar = "", Birthday = null, Sex = (GenderEnum)2, Email = "", Phone = "0510", RealName = "李筱凡", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)2, OrgId = 397849297412421, PosId = 397848197947717, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            new SysUser{ Id = 524200872341829, UserName = "李晓龙", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "李晓龙", Avatar = "", Birthday = null, Sex = (GenderEnum)1, Email = "", Phone = "李晓龙", RealName = "李晓龙", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)1, OrgId = 394281300517189, PosId = 252885263003723, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            new SysUser{ Id = 524200950927685, UserName = "张驰", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "张驰", Avatar = "", Birthday = null, Sex = (GenderEnum)1, Email = "", Phone = "张驰", RealName = "张驰", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)1, OrgId = 394281300517189, PosId = 252885263003723, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            //new SysUser{ Id = 527701525705029, UserName = "王丹丹", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "王丹丹", Avatar = "", Birthday = null, Sex = (GenderEnum)2, Email = "", Phone = "王丹丹", RealName = "王丹丹", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)2, OrgId = 394281300517189, PosId = 252885263003723, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            new SysUser{ Id = 529130986824005, UserName = "刘斌", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "刘斌", Avatar = "", Birthday = null, Sex = (GenderEnum)1, Email = "", Phone = "刘斌", RealName = "刘斌", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)1, OrgId = 394281300517189, PosId = 252885263003723, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            //new SysUser{ Id = 532240281153861, UserName = "曹建", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "曹建", Avatar = "", Birthday = null, Sex = (GenderEnum)1, Email = "", Phone = "曹建", RealName = "曹建", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)2, OrgId = 394281300517189, PosId = 252885263003723, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            new SysUser{ Id = 532240347251013, UserName = "枚秀全", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "枚秀全", Avatar = "", Birthday = null, Sex = (GenderEnum)1, Email = "", Phone = "枚秀全", RealName = "枚秀全", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)1, OrgId = 394281300517189, PosId = 252885263003723, JobNum = "", JobStatus = (JobStatusEnum)1, TenantId = 100001 },
            new SysUser{ Id = 666381151851141, UserName = "张朱卫", Password = "ea96693d3b1d41d936a9de011852f99d", NickName = "", Avatar = "", Birthday = null, Sex = (GenderEnum)1, Email = "", Phone = "张朱卫", RealName = "张朱卫", IdCard = "", Signature = "", Introduction = "", UserType = (UserTypeEnum)2, Remark = "", Status = (StatusEnum)1, OrgId = 394281300517189, PosId = 252885263003723, JobNum = "", JobStatus = (JobStatusEnum)1 },

        };
    }
}