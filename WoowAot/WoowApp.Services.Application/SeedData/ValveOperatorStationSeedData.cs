namespace WoowApp.Services.Application.SeedData;

public class ValveOperatorStationSeedData //: ISqlSugarEntitySeedData<ValveOperatorStation>
{
    public IEnumerable<ValveOperatorStation> HasData()
    {
        return
        [
            new ValveOperatorStation { Id = 520000000000001, Name = "检测位1", Description = "撬装检测位", AppKey = "10001", Password = "123456", HardwareVersion = HardwareVersionEnum.通用撬装V1, DistanceMeasurementType = DistanceMeasurementTypeEnum.高精度增量编码器},
        ];
    }
}