namespace WoowApp.Services.Application;

/// <summary>
/// 系统菜单表种子数据
/// </summary>
public partial class SysMenuSeedData : ISqlSugarEntitySeedData<SysMenu>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysMenu> HasData()
    {
        return new[]
        {
            new SysMenu{ Id = 252885263003710, Pid = 0, Type = (MenuTypeEnum)1, Name = "Dashboard", Path = "/dashboard", Component = "LAYOUT", Redirect = "/dashboard/analysis", Title = "数据面板", Icon = "ant-design:home-outlined", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 40, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003711, Pid = 252885263003710, Type = (MenuTypeEnum)2, Name = "Analysis", Path = "analysis", Component = "/dashboard/analysis/index", Title = "分析页", Icon = "ant-design:aim-outlined", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 100, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003712, Pid = 252885263003710, Type = (MenuTypeEnum)2, Name = "Workbench", Path = "workbench", Component = "/dashboard/workbench/index", Title = "工作台", Icon = "ant-design:ant-design-outlined", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 101, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003720, Pid = 0, Type = (MenuTypeEnum)1, Name = "sys", Path = "/sys", Component = "LAYOUT", Title = "系统管理", Icon = "ant-design:setting-outlined", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 50, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003750, Pid = 252885263003720, Type = (MenuTypeEnum)2, Name = "MenuManagement", Path = "/menu", Component = "/sys/admin/menu/index", Title = "菜单管理", Icon = "ep:menu", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 90, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003751, Pid = 252885263003750, Type = (MenuTypeEnum)3, Permission = "sysMenu:page", Title = "菜单查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 100, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003752, Pid = 252885263003750, Type = (MenuTypeEnum)3, Permission = "sysMenu:update", Title = "菜单编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 100, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003753, Pid = 252885263003750, Type = (MenuTypeEnum)3, Permission = "sysMenu:add", Title = "菜单增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 100, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003754, Pid = 252885263003750, Type = (MenuTypeEnum)3, Permission = "sysMenu:delete", Title = "菜单删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 100, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003775, Pid = 252885263003720, Type = (MenuTypeEnum)2, Name = "PasswordManagement", Path = "/password", Component = "/sys/admin/password/index", Title = "修改密码", Icon = "ant-design:eye-outlined", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 120, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003780, Pid = 0, Type = (MenuTypeEnum)1, Name = "platform", Path = "/platform", Component = "LAYOUT", Title = "平台管理", Icon = "ant-design:ant-design-outlined", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 60, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003900, Pid = 252885263003780, Type = (MenuTypeEnum)1, Name = "log", Path = "/log", Component = "LAYOUT", Title = "日志管理", Icon = "ant-design:carry-out-outlined", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 90, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 252885263003940, Pid = 252885263003780, Type = (MenuTypeEnum)1, Name = "file", Path = "/file", Component = "LAYOUT", Title = "文件管理", Icon = "ant-design:file-outlined", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 100, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 314383133692229, Pid = 0, Type = (MenuTypeEnum)1, Name = "app", Path = "/app", Component = "LAYOUT", Title = "业务管理", Icon = "ant-design:shop-outlined", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 315067155738949, Pid = 314383133692229, Type = (MenuTypeEnum)1, Path = "/base", Component = "LAYOUT", Title = "基础信息", Icon = "ant-design:database-outlined", HideMenu = false, IgnoreKeepAlive = true, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923737925, Pid = 252885263003780, Type = (MenuTypeEnum)2, Name = "RegionManagement", Path = "/region", Component = "/sys/admin/Region/Wrapper", Title = "行政区域", Icon = "ant-design:dribbble-square-filled", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923799365, Pid = 321725923737925, Type = (MenuTypeEnum)3, Permission = "Region:page", Title = "行政区域查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923799366, Pid = 321725923737925, Type = (MenuTypeEnum)3, Permission = "Region:detail", Title = "行政区域详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923799367, Pid = 321725923737925, Type = (MenuTypeEnum)3, Permission = "Region:add", Title = "行政区域增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923799368, Pid = 321725923737925, Type = (MenuTypeEnum)3, Permission = "Region:delete", Title = "行政区域删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923799369, Pid = 321725923737925, Type = (MenuTypeEnum)3, Permission = "Region:edit", Title = "行政区域编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923799370, Pid = 321725923737925, Type = (MenuTypeEnum)3, Permission = "Region:export", Title = "行政区域导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 321725923799371, Pid = 321725923737925, Type = (MenuTypeEnum)3, Permission = "Region:import", Title = "行政区域导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843652421, Pid = 315067155738949, Type = (MenuTypeEnum)2, Name = "ApplicationConfigCategoryManagement", Path = "/application_config_category", Component = "/sys/admin/ApplicationConfigCategory/index", Title = "应用配置分类管理", Icon = "ep:crop", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843701573, Pid = 327082843652421, Type = (MenuTypeEnum)3, Permission = "ApplicationConfigCategory:page", Title = "应用配置分类查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843701574, Pid = 327082843652421, Type = (MenuTypeEnum)3, Permission = "ApplicationConfigCategory:detail", Title = "应用配置分类详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843701575, Pid = 327082843652421, Type = (MenuTypeEnum)3, Permission = "ApplicationConfigCategory:add", Title = "应用配置分类增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843701576, Pid = 327082843652421, Type = (MenuTypeEnum)3, Permission = "ApplicationConfigCategory:delete", Title = "应用配置分类删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843701577, Pid = 327082843652421, Type = (MenuTypeEnum)3, Permission = "ApplicationConfigCategory:edit", Title = "应用配置分类编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843701578, Pid = 327082843652421, Type = (MenuTypeEnum)3, Permission = "ApplicationConfigCategory:export", Title = "应用配置分类导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327082843701579, Pid = 327082843652421, Type = (MenuTypeEnum)3, Permission = "ApplicationConfigCategory:import", Title = "应用配置分类导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824457541, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ApplicationConfigManagement", Path = "/application_config", Component = "/sys/admin/ApplicationConfig/Wrapper", Title = "应用配置管理", Icon = "ep:paperclip", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 80, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824494405, Pid = 327084824457541, Type = (MenuTypeEnum)3, Permission = "ApplicationConfig:page", Title = "应用配置查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824494406, Pid = 327084824457541, Type = (MenuTypeEnum)3, Permission = "ApplicationConfig:detail", Title = "应用配置详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824494407, Pid = 327084824457541, Type = (MenuTypeEnum)3, Permission = "ApplicationConfig:add", Title = "应用配置增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824494408, Pid = 327084824457541, Type = (MenuTypeEnum)3, Permission = "ApplicationConfig:delete", Title = "应用配置删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824494409, Pid = 327084824457541, Type = (MenuTypeEnum)3, Permission = "ApplicationConfig:edit", Title = "应用配置编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824494410, Pid = 327084824457541, Type = (MenuTypeEnum)3, Permission = "ApplicationConfig:export", Title = "应用配置导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 327084824494411, Pid = 327084824457541, Type = (MenuTypeEnum)3, Permission = "ApplicationConfig:import", Title = "应用配置导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407424325, Pid = 252885263003900, Type = (MenuTypeEnum)2, Name = "SysLogExManagement", Path = "/sys_log_ex", Component = "/sys/admin/SysLogEx/index", Title = "系统异常日志管理", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407534917, Pid = 344402407424325, Type = (MenuTypeEnum)3, Permission = "SysLogEx:page", Title = "系统异常日志查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407534918, Pid = 344402407424325, Type = (MenuTypeEnum)3, Permission = "SysLogEx:detail", Title = "系统异常日志详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407534919, Pid = 344402407424325, Type = (MenuTypeEnum)3, Permission = "SysLogEx:add", Title = "系统异常日志增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407534920, Pid = 344402407424325, Type = (MenuTypeEnum)3, Permission = "SysLogEx:delete", Title = "系统异常日志删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407534921, Pid = 344402407424325, Type = (MenuTypeEnum)3, Permission = "SysLogEx:edit", Title = "系统异常日志编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407534922, Pid = 344402407424325, Type = (MenuTypeEnum)3, Permission = "SysLogEx:export", Title = "系统异常日志导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344402407534923, Pid = 344402407424325, Type = (MenuTypeEnum)3, Permission = "SysLogEx:import", Title = "系统异常日志导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178750277, Pid = 252885263003900, Type = (MenuTypeEnum)2, Name = "SysLogOpManagement", Path = "/sys_log_op", Component = "/sys/admin/SysLogOp/index", Title = "系统操作日志管理", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178873157, Pid = 344403178750277, Type = (MenuTypeEnum)3, Permission = "SysLogOp:page", Title = "系统操作日志查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178873158, Pid = 344403178750277, Type = (MenuTypeEnum)3, Permission = "SysLogOp:detail", Title = "系统操作日志详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178873159, Pid = 344403178750277, Type = (MenuTypeEnum)3, Permission = "SysLogOp:add", Title = "系统操作日志增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178873160, Pid = 344403178750277, Type = (MenuTypeEnum)3, Permission = "SysLogOp:delete", Title = "系统操作日志删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178873161, Pid = 344403178750277, Type = (MenuTypeEnum)3, Permission = "SysLogOp:edit", Title = "系统操作日志编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178873162, Pid = 344403178750277, Type = (MenuTypeEnum)3, Permission = "SysLogOp:export", Title = "系统操作日志导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 344403178873163, Pid = 344403178750277, Type = (MenuTypeEnum)3, Permission = "SysLogOp:import", Title = "系统操作日志导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476602693, Pid = 252885263003720, Type = (MenuTypeEnum)2, Name = "SysUserV2Management", Path = "/sys_user_v2", Component = "/sys/admin/SysUserV2/Wrapper", Title = "账号管理", Icon = "ep:user", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 50, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476655941, Pid = 352176476602693, Type = (MenuTypeEnum)3, Permission = "SysUserV2:page", Title = "账号管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476655942, Pid = 352176476602693, Type = (MenuTypeEnum)3, Permission = "SysUserV2:detail", Title = "账号管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476655943, Pid = 352176476602693, Type = (MenuTypeEnum)3, Permission = "SysUserV2:add", Title = "账号管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476655944, Pid = 352176476602693, Type = (MenuTypeEnum)3, Permission = "SysUserV2:delete", Title = "账号管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476655945, Pid = 352176476602693, Type = (MenuTypeEnum)3, Permission = "SysUserV2:edit", Title = "账号管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476655946, Pid = 352176476602693, Type = (MenuTypeEnum)3, Permission = "SysUserV2:export", Title = "账号管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352176476655947, Pid = 352176476602693, Type = (MenuTypeEnum)3, Permission = "SysUserV2:import", Title = "账号管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779297605, Pid = 252885263003720, Type = (MenuTypeEnum)2, Name = "SysRoleV2Management", Path = "/sys_role_v2", Component = "/sys/admin/SysRoleV2/index", Title = "角色管理", Icon = "ep:picture-rounded", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 60, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779432773, Pid = 352979779297605, Type = (MenuTypeEnum)3, Permission = "SysRoleV2:page", Title = "角色管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779436869, Pid = 352979779297605, Type = (MenuTypeEnum)3, Permission = "SysRoleV2:detail", Title = "角色管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779436870, Pid = 352979779297605, Type = (MenuTypeEnum)3, Permission = "SysRoleV2:add", Title = "角色管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779436871, Pid = 352979779297605, Type = (MenuTypeEnum)3, Permission = "SysRoleV2:delete", Title = "角色管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779436872, Pid = 352979779297605, Type = (MenuTypeEnum)3, Permission = "SysRoleV2:edit", Title = "角色管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779436873, Pid = 352979779297605, Type = (MenuTypeEnum)3, Permission = "SysRoleV2:export", Title = "角色管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352979779436874, Pid = 352979779297605, Type = (MenuTypeEnum)3, Permission = "SysRoleV2:import", Title = "角色管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679237957, Pid = 252885263003720, Type = (MenuTypeEnum)2, Name = "SysPosV2Management", Path = "/sys_pos_v2", Component = "/sys/admin/SysPosV2/index", Title = "系统职位", Icon = "ep:goblet-square", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 40, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679319877, Pid = 352980679237957, Type = (MenuTypeEnum)3, Permission = "SysPosV2:page", Title = "系统职位查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679319878, Pid = 352980679237957, Type = (MenuTypeEnum)3, Permission = "SysPosV2:detail", Title = "系统职位详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679319879, Pid = 352980679237957, Type = (MenuTypeEnum)3, Permission = "SysPosV2:add", Title = "系统职位增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679319880, Pid = 352980679237957, Type = (MenuTypeEnum)3, Permission = "SysPosV2:delete", Title = "系统职位删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679319881, Pid = 352980679237957, Type = (MenuTypeEnum)3, Permission = "SysPosV2:edit", Title = "系统职位编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679319882, Pid = 352980679237957, Type = (MenuTypeEnum)3, Permission = "SysPosV2:export", Title = "系统职位导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980679319883, Pid = 352980679237957, Type = (MenuTypeEnum)3, Permission = "SysPosV2:import", Title = "系统职位导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746756421, Pid = 252885263003720, Type = (MenuTypeEnum)2, Name = "SysOrgV2Management", Path = "/sys_org_v2", Component = "/sys/admin/SysOrgV2/Wrapper", Title = "机构管理", Icon = "ep:ice-cream-round", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746838341, Pid = 352980746756421, Type = (MenuTypeEnum)3, Permission = "SysOrgV2:page", Title = "机构管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746838342, Pid = 352980746756421, Type = (MenuTypeEnum)3, Permission = "SysOrgV2:detail", Title = "机构管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746838343, Pid = 352980746756421, Type = (MenuTypeEnum)3, Permission = "SysOrgV2:add", Title = "机构管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746838344, Pid = 352980746756421, Type = (MenuTypeEnum)3, Permission = "SysOrgV2:delete", Title = "机构管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746838345, Pid = 352980746756421, Type = (MenuTypeEnum)3, Permission = "SysOrgV2:edit", Title = "机构管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746838346, Pid = 352980746756421, Type = (MenuTypeEnum)3, Permission = "SysOrgV2:export", Title = "机构管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 352980746838347, Pid = 352980746756421, Type = (MenuTypeEnum)3, Permission = "SysOrgV2:import", Title = "机构管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768585029, Pid = 252885263003780, Type = (MenuTypeEnum)2, Name = "SysConfigV2Management", Path = "/sys_config_v2", Component = "/sys/admin/SysConfigV2/index", Title = "系统配置管理", Icon = "ep:cpu", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768658757, Pid = 353954768585029, Type = (MenuTypeEnum)3, Permission = "SysConfigV2:page", Title = "系统配置查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768658758, Pid = 353954768585029, Type = (MenuTypeEnum)3, Permission = "SysConfigV2:detail", Title = "系统配置详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768658759, Pid = 353954768585029, Type = (MenuTypeEnum)3, Permission = "SysConfigV2:add", Title = "系统配置增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768658760, Pid = 353954768585029, Type = (MenuTypeEnum)3, Permission = "SysConfigV2:delete", Title = "系统配置删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768658761, Pid = 353954768585029, Type = (MenuTypeEnum)3, Permission = "SysConfigV2:edit", Title = "系统配置编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768658762, Pid = 353954768585029, Type = (MenuTypeEnum)3, Permission = "SysConfigV2:export", Title = "系统配置导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353954768658763, Pid = 353954768585029, Type = (MenuTypeEnum)3, Permission = "SysConfigV2:import", Title = "系统配置导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142755141, Pid = 252885263003940, Type = (MenuTypeEnum)2, Name = "SysFileV2Management", Path = "/sys_file_v2", Component = "/sys/admin/SysFileV2/index", Title = "文件审核管理", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142820677, Pid = 353966142755141, Type = (MenuTypeEnum)3, Permission = "SysFileV2:page", Title = "文件审核查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142820678, Pid = 353966142755141, Type = (MenuTypeEnum)3, Permission = "SysFileV2:detail", Title = "文件审核详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142820679, Pid = 353966142755141, Type = (MenuTypeEnum)3, Permission = "SysFileV2:add", Title = "文件审核增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142820680, Pid = 353966142755141, Type = (MenuTypeEnum)3, Permission = "SysFileV2:delete", Title = "文件审核删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142820681, Pid = 353966142755141, Type = (MenuTypeEnum)3, Permission = "SysFileV2:edit", Title = "文件审核编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142820682, Pid = 353966142755141, Type = (MenuTypeEnum)3, Permission = "SysFileV2:export", Title = "文件审核导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 353966142824773, Pid = 353966142755141, Type = (MenuTypeEnum)3, Permission = "SysFileV2:import", Title = "文件审核导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 378353793713349, Pid = 352176476602693, Type = (MenuTypeEnum)3, Name = "undefined_1", Permission = "SysUserV2:resetPwd", Title = "账号管理重置密码", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 378464923423045, Pid = 252885263003780, Type = (MenuTypeEnum)2, Name = "DatabaseV2", Path = "databasev2", Component = "/sys/admin/database/index", Title = "数据库管理", Icon = "ant-design:database-filled", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Tag = "V2", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516453495169349, Pid = 0, Type = (MenuTypeEnum)2, Name = "skid_mounted_manage", Path = "/skid-mounted-manage", Title = "撬装控制", Icon = "ep:connection", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818730309, Pid = 516453495169349, Type = (MenuTypeEnum)2, Name = "SkidMountedManageManagement", Path = "/skid_mounted_manage", Component = "/main/SkidMountedManage/index", Title = "撬装管理", Icon = "ep:connection", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = true, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818844997, Pid = 516462818730309, Type = (MenuTypeEnum)3, Permission = "SkidMountedManage:page", Title = "撬装管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818849093, Pid = 516462818730309, Type = (MenuTypeEnum)3, Permission = "SkidMountedManage:detail", Title = "撬装管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818849094, Pid = 516462818730309, Type = (MenuTypeEnum)3, Permission = "SkidMountedManage:add", Title = "撬装管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818849095, Pid = 516462818730309, Type = (MenuTypeEnum)3, Permission = "SkidMountedManage:delete", Title = "撬装管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818849096, Pid = 516462818730309, Type = (MenuTypeEnum)3, Permission = "SkidMountedManage:edit", Title = "撬装管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818849097, Pid = 516462818730309, Type = (MenuTypeEnum)3, Permission = "SkidMountedManage:export", Title = "撬装管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462818849098, Pid = 516462818730309, Type = (MenuTypeEnum)3, Permission = "SkidMountedManage:import", Title = "撬装管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819615045, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ValveAttrManageManagement", Path = "/valve_attr_manage", Component = "/main/ValveAttrManage/index", Title = "属性管理", Icon = "ep:price-tag", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 40, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819799365, Pid = 516462819615045, Type = (MenuTypeEnum)3, Permission = "ValveAttrManage:page", Title = "阀门属性管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819799366, Pid = 516462819615045, Type = (MenuTypeEnum)3, Permission = "ValveAttrManage:detail", Title = "阀门属性管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819799367, Pid = 516462819615045, Type = (MenuTypeEnum)3, Permission = "ValveAttrManage:add", Title = "阀门属性管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819799368, Pid = 516462819615045, Type = (MenuTypeEnum)3, Permission = "ValveAttrManage:delete", Title = "阀门属性管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819799369, Pid = 516462819615045, Type = (MenuTypeEnum)3, Permission = "ValveAttrManage:edit", Title = "阀门属性管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819799370, Pid = 516462819615045, Type = (MenuTypeEnum)3, Permission = "ValveAttrManage:export", Title = "阀门属性管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462819799371, Pid = 516462819615045, Type = (MenuTypeEnum)3, Permission = "ValveAttrManage:import", Title = "阀门属性管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820442437, Pid = 516453495169349, Type = (MenuTypeEnum)2, Name = "ValveAttrValueManageManagement", Path = "/valve_attr_value_manage", Component = "/main/ValveAttrValueManage/index", Title = "执行机构属性值管理", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 40, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820626757, Pid = 516462820442437, Type = (MenuTypeEnum)3, Permission = "ValveAttrValueManage:page", Title = "执行机构属性值管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820626758, Pid = 516462820442437, Type = (MenuTypeEnum)3, Permission = "ValveAttrValueManage:detail", Title = "执行机构属性值管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820626759, Pid = 516462820442437, Type = (MenuTypeEnum)3, Permission = "ValveAttrValueManage:add", Title = "执行机构属性值管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820626760, Pid = 516462820442437, Type = (MenuTypeEnum)3, Permission = "ValveAttrValueManage:delete", Title = "执行机构属性值管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820626761, Pid = 516462820442437, Type = (MenuTypeEnum)3, Permission = "ValveAttrValueManage:edit", Title = "执行机构属性值管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820626762, Pid = 516462820442437, Type = (MenuTypeEnum)3, Permission = "ValveAttrValueManage:export", Title = "执行机构属性值管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516462820626763, Pid = 516462820442437, Type = (MenuTypeEnum)3, Permission = "ValveAttrValueManage:import", Title = "执行机构属性值管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516464591302981, Pid = 516453495169349, Type = (MenuTypeEnum)2, Name = "valve_base", Path = "/valve-base", Title = "基础设置", Icon = "ep:notebook", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034685253, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ValveOperatorStationManageManagement", Path = "/valve_operator_station_manage", Component = "/main/ValveOperatorStationManage/index", Title = "操作工位管理", Icon = "ep:setting", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034742597, Pid = 516792034685253, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationManage:page", Title = "操作工位管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034746693, Pid = 516792034685253, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationManage:detail", Title = "操作工位管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034746694, Pid = 516792034685253, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationManage:add", Title = "操作工位管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034746695, Pid = 516792034685253, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationManage:delete", Title = "操作工位管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034746696, Pid = 516792034685253, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationManage:edit", Title = "操作工位管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034746697, Pid = 516792034685253, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationManage:export", Title = "操作工位管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 516792034746698, Pid = 516792034685253, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationManage:import", Title = "操作工位管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053606213, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobLogViewManagement", Path = "/valve_job_log_view", Component = "/main/ValveJobLogView/index", Title = "阀门离线诊断任务日志跟踪", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 270, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053671749, Pid = 517105053606213, Type = (MenuTypeEnum)3, Permission = "ValveJobLogView:page", Title = "阀门终检任务日志跟踪查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053671750, Pid = 517105053606213, Type = (MenuTypeEnum)3, Permission = "ValveJobLogView:detail", Title = "阀门终检任务日志跟踪详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053671751, Pid = 517105053606213, Type = (MenuTypeEnum)3, Permission = "ValveJobLogView:export", Title = "阀门终检任务日志跟踪导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053786437, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobViewManagement", Path = "/valve_job_view", Component = "/main/ValveJobView/index", Title = "任务跟踪", Icon = "ep:wind-power", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 200, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053815109, Pid = 517105053786437, Type = (MenuTypeEnum)3, Permission = "ValveJobView:page", Title = "阀门终检任务跟踪查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053815110, Pid = 517105053786437, Type = (MenuTypeEnum)3, Permission = "ValveJobView:detail", Title = "阀门终检任务跟踪详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 517105053815111, Pid = 517105053786437, Type = (MenuTypeEnum)3, Permission = "ValveJobView:export", Title = "阀门终检任务跟踪导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262638405, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobSnapshotViewManagement", Path = "/valve_job_snapshot_view", Component = "/main/ValveJobSnapshotView/index", Title = "阀门离线诊断快照查询", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 210, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262695749, Pid = 518151262638405, Type = (MenuTypeEnum)3, Permission = "ValveJobSnapshotView:page", Title = "阀门终检快照查询查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262695750, Pid = 518151262638405, Type = (MenuTypeEnum)3, Permission = "ValveJobSnapshotView:detail", Title = "阀门终检快照查询详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262695751, Pid = 518151262638405, Type = (MenuTypeEnum)3, Permission = "ValveJobSnapshotView:edit", Title = "阀门终检快照查询编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262695752, Pid = 518151262638405, Type = (MenuTypeEnum)3, Permission = "ValveJobSnapshotView:export", Title = "阀门终检快照查询导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262863685, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobOperatorStationViewManagement", Path = "/valve_job_operator_station_view", Component = "/main/ValveJobOperatorStationView/index", Title = "阀门离线诊断工位信息查询", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 230, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262900549, Pid = 518151262863685, Type = (MenuTypeEnum)3, Permission = "ValveJobOperatorStationView:page", Title = "阀门终检工位信息查询查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262900550, Pid = 518151262863685, Type = (MenuTypeEnum)3, Permission = "ValveJobOperatorStationView:detail", Title = "阀门终检工位信息查询详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262900551, Pid = 518151262863685, Type = (MenuTypeEnum)3, Permission = "ValveJobOperatorStationView:edit", Title = "阀门终检工位信息查询编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151262900552, Pid = 518151262863685, Type = (MenuTypeEnum)3, Permission = "ValveJobOperatorStationView:export", Title = "阀门终检工位信息查询导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151263039813, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobAttrValueViewManagement", Path = "/valve_job_attr_value_view", Component = "/main/ValveJobAttrValueView/index", Title = "阀门离线诊断参数查询", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 220, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151263072581, Pid = 518151263039813, Type = (MenuTypeEnum)3, Permission = "ValveJobAttrValueView:page", Title = "阀门终检参数查询查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151263072582, Pid = 518151263039813, Type = (MenuTypeEnum)3, Permission = "ValveJobAttrValueView:detail", Title = "阀门终检参数查询详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151263072583, Pid = 518151263039813, Type = (MenuTypeEnum)3, Permission = "ValveJobAttrValueView:edit", Title = "阀门终检参数查询编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 518151263072584, Pid = 518151263039813, Type = (MenuTypeEnum)3, Permission = "ValveJobAttrValueView:export", Title = "阀门终检参数查询导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015184197, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ValveFaultItemLevelManageManagement", Path = "/valve_fault_item_level_manage", Component = "/main/ValveFaultItemLevelManage/index", Title = "故障项等级管理", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 70, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015237445, Pid = 519600015184197, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemLevelManage:page", Title = "阀门故障项等级管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015241541, Pid = 519600015184197, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemLevelManage:detail", Title = "阀门故障项等级管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015241542, Pid = 519600015184197, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemLevelManage:add", Title = "阀门故障项等级管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015241543, Pid = 519600015184197, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemLevelManage:delete", Title = "阀门故障项等级管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015241544, Pid = 519600015184197, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemLevelManage:edit", Title = "阀门故障项等级管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015241545, Pid = 519600015184197, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemLevelManage:export", Title = "阀门故障项等级管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015241546, Pid = 519600015184197, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemLevelManage:import", Title = "阀门故障项等级管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015499589, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ValveFaultItemManageManagement", Path = "/valve_fault_item_manage", Component = "/main/ValveFaultItemManage/index", Title = "故障项管理", Icon = "ep:credit-card", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 60, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015532357, Pid = 519600015499589, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemManage:page", Title = "阀门故障项管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015532358, Pid = 519600015499589, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemManage:detail", Title = "阀门故障项管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015532359, Pid = 519600015499589, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemManage:add", Title = "阀门故障项管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015532360, Pid = 519600015499589, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemManage:delete", Title = "阀门故障项管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015532361, Pid = 519600015499589, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemManage:edit", Title = "阀门故障项管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015532362, Pid = 519600015499589, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemManage:export", Title = "阀门故障项管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015532363, Pid = 519600015499589, Type = (MenuTypeEnum)3, Permission = "ValveFaultItemManage:import", Title = "阀门故障项管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015794501, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ValveFaultCategoryManageManagement", Path = "/valve_fault_category_manage", Component = "/main/ValveFaultCategoryManage/index", Title = "故障分类管理", Icon = "ep:notification", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 50, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015823173, Pid = 519600015794501, Type = (MenuTypeEnum)3, Permission = "ValveFaultCategoryManage:page", Title = "阀门故障分类管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015823174, Pid = 519600015794501, Type = (MenuTypeEnum)3, Permission = "ValveFaultCategoryManage:detail", Title = "阀门故障分类管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015823175, Pid = 519600015794501, Type = (MenuTypeEnum)3, Permission = "ValveFaultCategoryManage:add", Title = "阀门故障分类管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015823176, Pid = 519600015794501, Type = (MenuTypeEnum)3, Permission = "ValveFaultCategoryManage:delete", Title = "阀门故障分类管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015823177, Pid = 519600015794501, Type = (MenuTypeEnum)3, Permission = "ValveFaultCategoryManage:edit", Title = "阀门故障分类管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015823178, Pid = 519600015794501, Type = (MenuTypeEnum)3, Permission = "ValveFaultCategoryManage:export", Title = "阀门故障分类管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600015823179, Pid = 519600015794501, Type = (MenuTypeEnum)3, Permission = "ValveFaultCategoryManage:import", Title = "阀门故障分类管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147702085, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobFaultItemViewManagement", Path = "/valve_job_fault_item_view", Component = "/main/ValveJobFaultItemView/index", Title = "阀门离线诊断任务故障项信息跟踪", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 260, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147751237, Pid = 519600147702085, Type = (MenuTypeEnum)3, Permission = "ValveJobFaultItemView:page", Title = "阀门终检任务故障项信息跟踪查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147751238, Pid = 519600147702085, Type = (MenuTypeEnum)3, Permission = "ValveJobFaultItemView:detail", Title = "阀门终检任务故障项信息跟踪详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147751239, Pid = 519600147702085, Type = (MenuTypeEnum)3, Permission = "ValveJobFaultItemView:export", Title = "阀门终检任务故障项信息跟踪导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147870021, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobFaultItemLevelViewManagement", Path = "/valve_job_fault_item_level_view", Component = "/main/ValveJobFaultItemLevelView/index", Title = "阀门离线诊断任务故障项等级跟踪", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 250, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147960133, Pid = 519600147870021, Type = (MenuTypeEnum)3, Permission = "ValveJobFaultItemLevelView:page", Title = "阀门终检任务故障项等级跟踪查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147960134, Pid = 519600147870021, Type = (MenuTypeEnum)3, Permission = "ValveJobFaultItemLevelView:detail", Title = "阀门终检任务故障项等级跟踪详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 519600147960135, Pid = 519600147870021, Type = (MenuTypeEnum)3, Permission = "ValveJobFaultItemLevelView:export", Title = "阀门终检任务故障项等级跟踪导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 520343599403333, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobOfflineReportViewManagement", Path = "/valve_job_offline_report_view", Component = "/main/ValveJobOfflineReportView/index", Title = "阀门离线诊断报告查询", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 280, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 520343599468869, Pid = 520343599403333, Type = (MenuTypeEnum)3, Permission = "ValveJobOfflineReportView:page", Title = "终检离线测诊报告查询查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 520343599468870, Pid = 520343599403333, Type = (MenuTypeEnum)3, Permission = "ValveJobOfflineReportView:detail", Title = "终检离线测诊报告查询详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 520343599468871, Pid = 520343599403333, Type = (MenuTypeEnum)3, Permission = "ValveJobOfflineReportView:export", Title = "终检离线测诊报告查询导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737806860613, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ValveOperatorStationSensorManageManagement", Path = "/valve_operator_station_sensor_manage", Component = "/main/ValveOperatorStationSensorManage/index", Title = "工位传感器设置管理", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807077701, Pid = 521737806860613, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationSensorManage:page", Title = "工位传感器设置管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807077702, Pid = 521737806860613, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationSensorManage:detail", Title = "工位传感器设置管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807077703, Pid = 521737806860613, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationSensorManage:edit", Title = "工位传感器设置管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807077704, Pid = 521737806860613, Type = (MenuTypeEnum)3, Permission = "ValveOperatorStationSensorManage:export", Title = "工位传感器设置管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807548741, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ValveJobOperatorStationSensorViewManagement", Path = "/valve_job_operator_station_sensor_view", Component = "/main/ValveJobOperatorStationSensorView/index", Title = "阀门离线诊断工位传感器设置快照", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 240, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807745349, Pid = 521737807548741, Type = (MenuTypeEnum)3, Permission = "ValveJobOperatorStationSensorView:page", Title = "任务传感器设置快照跟踪查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807745350, Pid = 521737807548741, Type = (MenuTypeEnum)3, Permission = "ValveJobOperatorStationSensorView:detail", Title = "任务传感器设置快照跟踪详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 521737807745351, Pid = 521737807548741, Type = (MenuTypeEnum)3, Permission = "ValveJobOperatorStationSensorView:export", Title = "任务传感器设置快照跟踪导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524165186806085, Pid = 516453495169349, Type = (MenuTypeEnum)1, Name = "valve_job_view_dir", Path = "/valve_job_view_dir", Title = "检验任务", Icon = "ep:odometer", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 50, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524165964075333, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "CalibrationManagement", Path = "/valve_job_calibration", Component = "/main/Calibration/index", Title = "传感器校准", Icon = "ep:stopwatch", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524193484468549, Pid = 524165964075333, Type = (MenuTypeEnum)3, Name = "undefined_3", Permission = "Calibration:startJob", Title = "传感器校准创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524193666224453, Pid = 524165964075333, Type = (MenuTypeEnum)3, Name = "undefined_3_4", Permission = "Calibration:stop", Title = "传感器校准中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524193839878469, Pid = 524165964075333, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5", Permission = "Calibration:getKeyData", Title = "传感器校准获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524194028450117, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "DeadZoneTestManagement", Path = "/valve_job_dead_zone_test", Component = "/main/DeadZoneTest/index", Title = "死区测试", Icon = "ep:guide", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 100, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524194336784709, Pid = 524194028450117, Type = (MenuTypeEnum)3, Name = "undefined_3_8", Permission = "DeadZoneTest:startJob", Title = "死区测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524194560401733, Pid = 524194028450117, Type = (MenuTypeEnum)3, Name = "undefined_3_4_9", Permission = "DeadZoneTest:stop", Title = "死区测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524194703593797, Pid = 524194028450117, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_10", Permission = "DeadZoneTest:getKeyData", Title = "死区测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524195153551685, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "RampTestManagement", Path = "/valve_job_ramp_test", Component = "/main/RampTest/index", Title = "动态测试", Icon = "ant-design:up-outlined", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 60, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524195347812677, Pid = 524195153551685, Type = (MenuTypeEnum)3, Name = "undefined_3_12", Permission = "RampTest:startJob", Title = "动态测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524195500335429, Pid = 524195153551685, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13", Permission = "RampTest:stop", Title = "动态测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524195588030789, Pid = 524195153551685, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14", Permission = "RampTest:getKeyData", Title = "动态测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524195972202821, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "StartingAndEndingTestManagement", Path = "/valve_job_starting_and_ending_test", Component = "/main/StartingAndEndingTest/index", Title = "始终点偏差测试", Icon = "ep:cellphone", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 80, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524196275794245, Pid = 524195972202821, Type = (MenuTypeEnum)3, Name = "undefined_3_12_16", Permission = "StartingAndEndingTest:startJob", Title = "始终点偏差测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524196390650181, Pid = 524195972202821, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_17", Permission = "StartingAndEndingTest:stop", Title = "始终点偏差测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524196529713477, Pid = 524195972202821, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_17_18", Permission = "StartingAndEndingTest:getKeyData", Title = "始终点偏差测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524196909052229, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "StepRespTestManagement", Path = "/valve_job_step_resp_test", Component = "/main/StepRespTest/index", Title = "阶跃响应测试", Icon = "ep:histogram", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 70, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524197113811269, Pid = 524196909052229, Type = (MenuTypeEnum)3, Name = "undefined_3_12_20", Permission = "StepRespTest:startJob", Title = "阶跃响应测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524197258961221, Pid = 524196909052229, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_21", Permission = "StepRespTest:stop", Title = "阶跃响应测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524197338636613, Pid = 524196909052229, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_22", Permission = "StepRespTest:getKeyData", Title = "阶跃响应测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524201675698501, Pid = 517105053786437, Type = (MenuTypeEnum)3, Name = "undefined_1", Permission = "ValveJobView:uploadServer", Title = "阀门终检任务上传到服务器", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 524840816849221, Pid = 517105053786437, Type = (MenuTypeEnum)3, Name = "undefined_1", Permission = "ValveJobView:stop", Title = "阀门终检任务跟踪停止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544724922693, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "ValveAttrGroupManageManagement", Path = "/valve_attr_group_manage", Component = "/main/ValveAttrGroupManage/index", Title = "属性分组管理", Icon = "ep:orange", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544725020997, Pid = 529544724922693, Type = (MenuTypeEnum)3, Permission = "ValveAttrGroupManage:page", Title = "阀门属性分组管理查询", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544725025093, Pid = 529544724922693, Type = (MenuTypeEnum)3, Permission = "ValveAttrGroupManage:detail", Title = "阀门属性分组管理详情", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544725025094, Pid = 529544724922693, Type = (MenuTypeEnum)3, Permission = "ValveAttrGroupManage:add", Title = "阀门属性分组管理增加", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544725025095, Pid = 529544724922693, Type = (MenuTypeEnum)3, Permission = "ValveAttrGroupManage:delete", Title = "阀门属性分组管理删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544725025096, Pid = 529544724922693, Type = (MenuTypeEnum)3, Permission = "ValveAttrGroupManage:edit", Title = "阀门属性分组管理编辑", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544725025097, Pid = 529544724922693, Type = (MenuTypeEnum)3, Permission = "ValveAttrGroupManage:export", Title = "阀门属性分组管理导出", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 529544725025098, Pid = 529544724922693, Type = (MenuTypeEnum)3, Permission = "ValveAttrGroupManage:import", Title = "阀门属性分组管理导入", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 0, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 531882459939141, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "OneStepManagement", Path = "/valve_job_one_step", Component = "/main/OneStep/index", Title = "综合测试", Icon = "ant-design:codepen-outlined", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Tag = "HOT", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 531882882777413, Pid = 531882459939141, Type = (MenuTypeEnum)3, Name = "未命名2", Permission = "OneStep:getComboId", Title = "一键测诊获取组合Id", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 532321966678341, Pid = 517105053786437, Type = (MenuTypeEnum)3, Name = "undefined_1_1", Permission = "ValveJobView:editTemplate", Title = "阀门终检任务设置模板", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 535064581615941, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "LeakageTestManagement", Path = "/valve_job_leakage_test", Component = "/main/LeakageTest/index", Title = "泄漏量测试", Icon = "ep:cold-drink", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 190, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 535064661938501, Pid = 535064581615941, Type = (MenuTypeEnum)3, Name = "undefined_3_8_2", Permission = "LeakageTest:startJob", Title = "泄漏量测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 535064694968645, Pid = 535064581615941, Type = (MenuTypeEnum)3, Name = "undefined_3_4_9_3", Permission = "LeakageTest:stop", Title = "泄漏量测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 535064770953541, Pid = 535064581615941, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_10_4", Permission = "LeakageTest:getKeyData", Title = "泄漏量测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566594818265413, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ResolutionTestManagement", Path = "/valve_job_resolution_test", Component = "/main/ResolutionTest/index", Title = "分辨率测试", Icon = "ep:picture-rounded", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 110, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566594990682437, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "SensitivityTestManagement_2", Path = "/valve_job_sensitivity_test", Component = "/main/SensitivityTest/index", Title = "灵敏度测试", Icon = "ep:sunrise", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 150, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566595241472325, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "SpeedTestManagement", Path = "/valve_job_speed_test", Component = "/main/SpeedTest/index", Title = "行程速度测试", Icon = "ep:trend-charts", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 130, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566595504316741, Pid = 566594818265413, Type = (MenuTypeEnum)3, Name = "undefined_3_12_4", Permission = "ResolutionTest:startJob", Title = "分辨率测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566595616899397, Pid = 566594818265413, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_5", Permission = "ResolutionTest:stop", Title = "分辨率测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566595813343557, Pid = 566594818265413, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_6", Permission = "ResolutionTest:getKeyData", Title = "分辨率测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566595991437637, Pid = 566594990682437, Type = (MenuTypeEnum)3, Name = "undefined_3_12_7", Permission = "SensitivityTest:startJob", Title = "灵敏度测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566596071813445, Pid = 566594990682437, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_8", Permission = "SensitivityTest:stop", Title = "灵敏度测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566596179460421, Pid = 566594990682437, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_9", Permission = "SensitivityTest:getKeyData", Title = "灵敏度测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566596458774853, Pid = 566595241472325, Type = (MenuTypeEnum)3, Name = "undefined_3_12_10", Permission = "SpeedTest:startJob", Title = "行程速度测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566596538118469, Pid = 566595241472325, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_11", Permission = "SpeedTest:stop", Title = "行程速度测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566596637196613, Pid = 566595241472325, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_12", Permission = "SpeedTest:getKeyData", Title = "行程速度测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566925753848133, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "HysteresisTestManagement_1", Path = "/valve_job_hysteresis_test", Component = "/main/HysteresisTest/index", Title = "性能测试", Icon = "ep:scale-to-original", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 140, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566925912060229, Pid = 566925753848133, Type = (MenuTypeEnum)3, Name = "undefined_3_12_2", Permission = "HysteresisTest:startJob", Title = "回差测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566925980045637, Pid = 566925753848133, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_3", Permission = "HysteresisTest:stop", Title = "回差测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 566926075760965, Pid = 566925753848133, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_4", Permission = "HysteresisTest:getKeyData", Title = "回差测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567621744963909, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "TransducerTestManagement", Path = "/valve_job_transducer_test", Component = "/main/TransducerTest/index", Title = "I/P测试", Icon = "ep:iphone", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 160, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567621846327621, Pid = 567621744963909, Type = (MenuTypeEnum)3, Name = "undefined_3_12_2", Permission = "TransducerTest:startJob", Title = "I/P测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567621945848133, Pid = 567621744963909, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_3", Permission = "TransducerTest:stop", Title = "I/P测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567622054003013, Pid = 567621744963909, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_4", Permission = "TransducerTest:getKeyData", Title = "I/P测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567622354985285, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "FrequencyTestManagement", Path = "/valve_job_frequency_test", Component = "/main/FrequencyTest/index", Title = "频率响应测试", Icon = "ep:fries", HideMenu = true, IgnoreKeepAlive = false, OrderNo = 120, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567622659998021, Pid = 567622354985285, Type = (MenuTypeEnum)3, Name = "undefined_3_12_6", Permission = "FrequencyTest:startJob", Title = "频率响应测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567622731649349, Pid = 567622354985285, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_7", Permission = "FrequencyTest:stop", Title = "频率响应测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 567622807793989, Pid = 567622354985285, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_8", Permission = "FrequencyTest:getKeyData", Title = "频率响应测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228041503045, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ShellTestManagement", Path = "/valve_job_shell_test", Component = "/main/ShellTest/index", Title = "壳体试验", Icon = "ep:postcard", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228183269701, Pid = 582228041503045, Type = (MenuTypeEnum)3, Name = "undefined_3_12_2", Permission = "ShellTest:startJob", Title = "壳体试验创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228401238341, Pid = 582228041503045, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_3", Permission = "ShellTest:stop", Title = "壳体试验中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228452684101, Pid = 582228041503045, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_4", Permission = "ShellTest:getKeyData", Title = "壳体试验获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228655284549, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "AirTightTestManagement", Path = "/valve_job_air_tight_test", Component = "/main/AirTightTest/index", Title = "气密性检测", Icon = "ep:credit-card", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 40, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228801888581, Pid = 582228655284549, Type = (MenuTypeEnum)3, Name = "undefined_3_12_6", Permission = "AirTightTest:startJob", Title = "气密性检测创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228883730757, Pid = 582228655284549, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_7", Permission = "AirTightTest:stop", Title = "气密性检测中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582228986704197, Pid = 582228655284549, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_8", Permission = "AirTightTest:getKeyData", Title = "气密性检测获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582229229388101, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "SwitchActionTestManagement", Path = "/valve_job_switch_action_test", Component = "/main/SwitchActionTest/index", Title = "动作测试试验", Icon = "ep:switch", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 50, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582229439340869, Pid = 582229229388101, Type = (MenuTypeEnum)3, Name = "undefined_3_12_10", Permission = "SwitchActionTest:startJob", Title = "动作测试试验创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582229554569541, Pid = 582229229388101, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_11", Permission = "SwitchActionTest:stop", Title = "动作测试试验中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 582229641978181, Pid = 582229229388101, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_12", Permission = "SwitchActionTest:getKeyData", Title = "动作测试试验获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 583919804035397, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ActionTimeTestManagement", Path = "/valve_job_action_time_test", Component = "/main/ActionTimeTest/index", Title = "动作时间试验", Icon = "ep:timer", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 90, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 583920049348933, Pid = 583919804035397, Type = (MenuTypeEnum)3, Name = "undefined_3_12_2", Permission = "ActionTimeTest:startJob", Title = "动作时间试验创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 583920262066501, Pid = 583919804035397, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_3", Permission = "ActionTimeTest:stop", Title = "动作时间试验中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 583920343650629, Pid = 583919804035397, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_4", Permission = "ActionTimeTest:getKeyData", Title = "动作时间试验获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589587285389637, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ThrustTestManagement", Path = "/valve_job_thrust_test", Component = "/main/ThrustTest/index", Title = "推力测试试验", Icon = "ep:odometer", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 170, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589587420012869, Pid = 589587285389637, Type = (MenuTypeEnum)3, Name = "undefined_3_12_2", Permission = "ThrustTest:startJob", Title = "推力测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589587470393669, Pid = 589587285389637, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_3", Permission = "ThrustTest:stop", Title = "推力测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589587534573893, Pid = 589587285389637, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_4", Permission = "ThrustTest:getKeyData", Title = "推力测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589588060811589, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ActionLifeTestManagement", Path = "/valve_job_action_life_test", Component = "/main/ActionLifeTest/index", Title = "动作寿命测试", Icon = "ep:link", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 180, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589588173025605, Pid = 589588060811589, Type = (MenuTypeEnum)3, Name = "undefined_3_12_6", Permission = "ActionLifeTest:startJob", Title = "动作寿命测试创建并开始任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 10, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589588220576069, Pid = 589588060811589, Type = (MenuTypeEnum)3, Name = "undefined_3_4_13_7", Permission = "ActionLifeTest:stop", Title = "动作寿命测试中止任务", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 20, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 589588284555589, Pid = 589588060811589, Type = (MenuTypeEnum)3, Name = "undefined_3_4_5_14_8", Permission = "ActionLifeTest:getKeyData", Title = "动作寿命测试获取关键数据", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 30, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 616181904249157, Pid = 517105053786437, Type = (MenuTypeEnum)3, Name = "undefined_1", Permission = "ValveJobView:delete", Title = "阀门终检任务删除", HideMenu = false, IgnoreKeepAlive = false, OrderNo = 40, HiddenBreadcrumb = false, Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 646918516044421, Pid = 524165186806085, Type = (MenuTypeEnum)2, Name = "ScanCodeTest", Path = "/scan_code_test", Component = "/main/ScanCodeTest/index", Permission = "", Redirect = "", FrameSrc = "", Title = "扫码测试", Icon = "ant-design:scan-outlined", HideMenu = false, IgnoreKeepAlive = false, CurrentActiveMenu = "", OrderNo = 10, HiddenBreadcrumb = false, Tag = "NEW", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 667889481720453, Pid = 516464591302981, Type = (MenuTypeEnum)2, Name = "MesUserManage", Path = "/mes_user_manage", Component = "/main/MesUserManage/index", Permission = "", Redirect = "", FrameSrc = "", Title = "MES用户绑定", Icon = "ep:user", HideMenu = false, IgnoreKeepAlive = false, CurrentActiveMenu = "", OrderNo = 90, HiddenBreadcrumb = false, Tag = "", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 667889951068805, Pid = 667889481720453, Type = (MenuTypeEnum)3, Name = "null_1", Path = "", Component = "", Permission = "MesUserManage:page", Redirect = "", FrameSrc = "", Title = "MES用户绑定查询", Icon = "", HideMenu = false, IgnoreKeepAlive = false, CurrentActiveMenu = "", OrderNo = 10, HiddenBreadcrumb = false, Tag = "", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 667890127585925, Pid = 667889481720453, Type = (MenuTypeEnum)3, Name = "null_2", Path = "", Component = "", Permission = "MesUserManage:detail", Redirect = "", FrameSrc = "", Title = "MES用户绑定详情", Icon = "", HideMenu = false, IgnoreKeepAlive = false, CurrentActiveMenu = "", OrderNo = 20, HiddenBreadcrumb = false, Tag = "", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 667890223682181, Pid = 667889481720453, Type = (MenuTypeEnum)3, Name = "null_3", Path = "", Component = "", Permission = "MesUserManage:add", Redirect = "", FrameSrc = "", Title = "MES用户绑定增加", Icon = "", HideMenu = false, IgnoreKeepAlive = false, CurrentActiveMenu = "", OrderNo = 30, HiddenBreadcrumb = false, Tag = "", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 667890318500485, Pid = 667889481720453, Type = (MenuTypeEnum)3, Name = "null_4", Path = "", Component = "", Permission = "MesUserManage:delete", Redirect = "", FrameSrc = "", Title = "MES用户绑定删除", Icon = "", HideMenu = false, IgnoreKeepAlive = false, CurrentActiveMenu = "", OrderNo = 40, HiddenBreadcrumb = false, Tag = "", Fullpage = false, Affix = false, IsDevelopment = false },
            new SysMenu{ Id = 667890402554501, Pid = 667889481720453, Type = (MenuTypeEnum)3, Name = "null_5", Path = "", Component = "", Permission = "MesUserManage:edit", Redirect = "", FrameSrc = "", Title = "MES用户绑定编辑", Icon = "", HideMenu = false, IgnoreKeepAlive = false, CurrentActiveMenu = "", OrderNo = 50, HiddenBreadcrumb = false, Tag = "", Fullpage = false, Affix = false, IsDevelopment = false },

        };
    }
}