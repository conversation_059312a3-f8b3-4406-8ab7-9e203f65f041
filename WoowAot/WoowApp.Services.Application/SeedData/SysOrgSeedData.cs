

namespace WoowApp.Services.Application;

/// <summary>
/// 系统机构表种子数据
/// </summary>
public partial class SysOrgSeedData : ISqlSugarEntitySeedData<SysOrg>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysOrg> HasData()
    {
        return new[]
        {
            new SysOrg{ Id = 394281300517189, Pid = 0, Name = "无锡智能自控工程股份有限公司", Code = "100", SortId = 0, Remark = "无锡智能自控工程股份有限公司", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 252885263003720, Pid = 394281300517189, Name = "无锡莱谱尔科技有限公司", Code = "1001", SortId = 0, Remark = "无锡莱谱尔科技有限公司", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 252885263003721, Pid = 252885263003720, Name = "研发三部", Code = "100101", SortId = 0, Remark = "研发三部", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 252885263003730, Pid = 394281300517189, Name = "行政证券营运中心", Code = "1002", SortId = 0, Remark = "行政证券营运中心", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 252885263003731, Pid = 252885263003730, Name = "审计法务部", Code = "100201", SortId = 0, Remark = "审计法务部", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 252885263003732, Pid = 252885263003730, Name = "综合管理与人力资源部", Code = "100202", SortId = 0, Remark = "综合管理与人力资源部", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 252885263003733, Pid = 252885263003730, Name = "财务部", Code = "100203", SortId = 0, Remark = "财务部", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 394283266310469, Pid = 394281300517189, Name = "销售市场营运中心", Code = "1003", SortId = 0, Remark = "销售市场营运中心", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 394283477573957, Pid = 394281300517189, Name = "生产制造营运中心", Code = "1004", SortId = 0, Remark = "生产制造营运中心", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 394283561685317, Pid = 394281300517189, Name = "产品技术运营中心", Code = "1005", SortId = 0, Remark = "产品技术运营中心", Status = (StatusEnum) 1, TenantId = 100001 },
            new SysOrg{ Id = 394283711013189, Pid = 394281300517189, Name = "总工程师办公室", Code = "1006", SortId = 0, Remark = "总工程师办公室", Status = (StatusEnum) 1, TenantId = 100001 },

        };
    }
}