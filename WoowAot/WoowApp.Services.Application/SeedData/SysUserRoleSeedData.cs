namespace WoowApp.Services.Application;

/// <summary>
/// 系统用户角色表种子数据
/// </summary>
public partial class SysUserRoleSeedData : ISqlSugarEntitySeedData<SysUserRole>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysUserRole> HasData()
    {
        return new[]
        {
            new SysUserRole{ Id = 524200697819461, UserId = 252885263003721, RoleId = 252885263003721, TenantId = 100001 },
            //new SysUserRole{ Id = 524200732696901, UserId = 252885263003722, RoleId = 252885263003722, TenantId = 100001 },
            new SysUserRole{ Id = 645516726362756, UserId = 524200872341829, RoleId = 252885263003721 },
            new SysUserRole{ Id = 645516726362757, UserId = 524200872341829, RoleId = 645515515314821 },
            new SysUserRole{ Id = 645519086211717, UserId = 532240347251013, RoleId = 645515515314821 },
            new SysUserRole{ Id = 645519109415557, UserId = 532240281153861, RoleId = 645515515314821 },
            new SysUserRole{ Id = 645519153808005, UserId = 529130986824005, RoleId = 645515515314821 },
            //new SysUserRole{ Id = 645519182582405, UserId = 527701525705029, RoleId = 645515515314821 },
            new SysUserRole{ Id = 645519204766341, UserId = 524200950927685, RoleId = 645515515314821 },
            //new SysUserRole{ Id = 645519245623941, UserId = 419142601933125, RoleId = 645515515314821 },
            new SysUserRole{ Id = 666381251170949, UserId = 666381151851141, RoleId = 645515515314821 },
        };
    }
}