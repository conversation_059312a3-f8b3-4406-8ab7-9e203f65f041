
namespace WoowApp.Services.Application.SeedData;

/// <summary>
/// 系统机构表种子数据
/// </summary>
public partial class ValveOperatorStationSensorSeedData //: ISqlSugarEntitySeedData<ValveOperatorStationSensor>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<ValveOperatorStationSensor> HasData()
    {
        return
        [
            // 通用V1
            new ValveOperatorStationSensor(){ SensorName=CoilRegisterDeviceEnum.电动开关阀开信号.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.开关量传感器, SortId = 1, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=CoilRegisterDeviceEnum.电动开关阀关信号.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.开关量传感器, SortId = 2, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=CoilRegisterDeviceEnum.气动开关阀1电磁阀.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.开关量传感器, SortId = 3, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=CoilRegisterDeviceEnum.气动开关阀2电磁阀.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.开关量传感器, SortId = 4, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            new(){ SensorName=CoilRegisterDeviceEnum.变频器启动信号.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.开关量传感器, SortId = 6, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=CoilRegisterDeviceEnum.变频器故障应答信号.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.开关量传感器, SortId = 7, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=CoilRegisterDeviceEnum.声光报警输出.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.开关量传感器, SortId = 8, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            new(){ SensorName=DiscreteRegisterDeviceEnum.电动开关阀开位.ToString(), SerialNumber = 1, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 101, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.电动开关阀关位.ToString(), SerialNumber = 2, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 102, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.气动开关阀1开位.ToString(), SerialNumber = 3, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 103, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.气动开关阀1关位.ToString(), SerialNumber = 4, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 104, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.气动开关阀2开位.ToString(), SerialNumber = 5, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 105, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.气动开关阀2关位.ToString(), SerialNumber = 6, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 106, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            new(){ SensorName=DiscreteRegisterDeviceEnum.变频器故障.ToString(), SerialNumber = 9, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 109, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.变频器运行.ToString(), SerialNumber = 10, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 110, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.变频器就绪.ToString(), SerialNumber = 11, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 111, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.变频器报警.ToString(), SerialNumber = 12, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 112, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=DiscreteRegisterDeviceEnum.急停开关.ToString(), SerialNumber = 14, Inversion=false, SensorType= SensorTypeEnum.开关量传感器, SortId = 114, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            new(){ SensorName=HoldingRegisterDeviceEnum.气动调节阀1控制信号.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.模拟量传感器, SortId = 301, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=HoldingRegisterDeviceEnum.气动调节阀2控制信号.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.模拟量传感器, SortId = 302, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=HoldingRegisterDeviceEnum.气动调节阀3控制信号.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.模拟量传感器, SortId = 303, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=HoldingRegisterDeviceEnum.变频器调节信号.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.模拟量传感器, SortId = 304, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            new(){ SensorName=InputRegisterDeviceEnum.电动开关阀阀位反馈.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.模拟量传感器, SortId = 201, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.气动调节阀1阀位反馈.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.模拟量传感器, SortId = 202, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.气动调节阀2阀位反馈.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.模拟量传感器, SortId = 203, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.气动调节阀3阀位反馈.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.模拟量传感器, SortId = 204, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            
            new(){ SensorName=InputRegisterDeviceEnum.阀前压力.ToString(), SerialNumber = 9, SensorType= SensorTypeEnum.模拟量传感器, SortId = 209, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(表压)", MeasureMin =0 , MeasureMax = 1000, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.压差.ToString(), SerialNumber = 10, SensorType= SensorTypeEnum.模拟量传感器, SortId = 210, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa", MeasureMin =0 , MeasureMax = 300, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            new(){ SensorName=InputRegisterDeviceEnum.环境温度.ToString(), SerialNumber = 11, SensorType= SensorTypeEnum.模拟量传感器, SortId = 211, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="℃", MeasureMin = 0, MeasureMax = 100, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.流量计.ToString(), SerialNumber = 13, SensorType= SensorTypeEnum.模拟量传感器, SortId = 212, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin = 0, MeasureMax = 100, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.液位计.ToString(), SerialNumber = 12, SensorType= SensorTypeEnum.模拟量传感器, SortId = 213, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mm", MeasureMin = 0, MeasureMax = 600, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.变频器转速.ToString(), SerialNumber = 15, SensorType= SensorTypeEnum.模拟量传感器, SortId = 215, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="rpm", MeasureMin = 0, MeasureMax = 1425, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.通用撬装V1},
            new(){ SensorName=InputRegisterDeviceEnum.变频器电流.ToString(), SerialNumber = 16, SensorType= SensorTypeEnum.模拟量传感器, SortId = 216, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mA", MeasureMin = 0, MeasureMax = 7300, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.通用撬装V1},


            //new(){ SensorName=DiscreteRegisterDeviceEnum.急停开关.ToString(), SerialNumber = 5, Inversion=false, SensorType= SensorTypeEnum.开关量传感器, SortId = 5, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=CoilRegisterDeviceEnum.指标灯红.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.开关量传感器, SortId = 8, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.指标灯黄.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.开关量传感器, SortId = 6, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.指标灯绿.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.开关量传感器, SortId = 7, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=CoilRegisterDeviceEnum.增量编码调零.ToString(), SerialNumber = 32, SensorType= SensorTypeEnum.开关量传感器, SortId = 17, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.比例阀自整定.ToString(), SerialNumber = 31, SensorType= SensorTypeEnum.开关量传感器, SortId = 18, HardwareVersion= HardwareVersionEnum.通用撬装V1},


            //new(){ SensorName=HoldingRegisterDeviceEnum.定位器输出电流.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.数字量传感器, SortId = 301, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.比例阀1压力.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.模拟量传感器, SortId = 302, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(g)", MeasureMin = 0, MeasureMax = 900 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000,  HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.比例阀2压力.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.模拟量传感器, SortId = 302, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(g)", MeasureMin = 0, MeasureMax = 900 , AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000,  HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=InputRegisterDeviceEnum.A口压力.ToString(), SerialNumber = 17, SensorType= SensorTypeEnum.模拟量传感器, SortId = 208, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=InputRegisterDeviceEnum.B口压力.ToString(), SerialNumber = 18, SensorType= SensorTypeEnum.模拟量传感器, SortId = 209, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=InputRegisterDeviceEnum.气源压力.ToString(), SerialNumber = 19, SensorType= SensorTypeEnum.模拟量传感器, SortId = 210, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=InputRegisterDeviceEnum.大气压力.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.模拟量传感器, SortId = 211, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 200, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=InputRegisterDeviceEnum.AO反馈1.ToString(), SerialNumber = 9, SensorType= SensorTypeEnum.数字量传感器, SortId = 212,MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000,  HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=InputRegisterDeviceEnum.AO反馈2.ToString(), SerialNumber = 11, SensorType= SensorTypeEnum.数字量传感器, SortId = 214,MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000,  HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=InputRegisterDeviceEnum.比例阀FB1.ToString(), SerialNumber = 10, SensorType= SensorTypeEnum.模拟量传感器, SortId = 213, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(g)", MeasureMin = 0, MeasureMax = 900 , AnalogUnit = "mv", AnalogMin =495, AnalogMax = 2475,  HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=InputRegisterDeviceEnum.比例阀FB2.ToString(), SerialNumber = 12, SensorType= SensorTypeEnum.模拟量传感器, SortId = 215, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(g)", MeasureMin = 0, MeasureMax = 900 , AnalogUnit = "mv", AnalogMin =495, AnalogMax = 2475,  HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=InputRegisterDeviceEnum.定位器电流反馈.ToString(), SerialNumber = 13, SensorType= SensorTypeEnum.数字量传感器, SortId = 216,MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000,  HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=InputRegisterDeviceEnum.增量编码器.ToString(), SerialNumber = 23, SensorType= SensorTypeEnum.模拟量传感器, SortId = 221, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mm", MeasureMin =0 , MeasureMax = 100, AnalogUnit = "脉冲数", AnalogMin =30000, AnalogMax = 40000 , HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=InputRegisterDeviceEnum.高精度增量编码器.ToString(), SerialNumber = 21, SensorType= SensorTypeEnum.高精度模拟量传感器, SortId = 221, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mm", MeasureMin =0 , MeasureMax = 100, AnalogUnit = "脉冲数", AnalogMin =10000000, AnalogMax = 10020000, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=InputRegisterDeviceEnum.角行程高精度增量编码器.ToString(), SerialNumber = 21, SensorType= SensorTypeEnum.高精度模拟量传感器, SortId = 221, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mm", MeasureMin =0 , MeasureMax = 360, AnalogUnit = "脉冲数", AnalogMin =10000000, AnalogMax = 10004000, HardwareVersion= HardwareVersionEnum.通用撬装V1},

            //new(){ SensorName=DiscreteRegisterDeviceEnum.主控急停开关.ToString(), SerialNumber = 38, Inversion=true, SensorType= SensorTypeEnum.开关量传感器, SortId = 6, HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.环境温度.ToString(), SerialNumber = 26, SensorType= SensorTypeEnum.模拟量传感器, SortId = 222, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="℃", MeasureMin = 0, MeasureMax = 100, AnalogMin = 0, AnalogMax = 1000, AnalogUnit = "0.1℃", HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.环境湿度.ToString(), SerialNumber = 27, SensorType= SensorTypeEnum.模拟量传感器, SortId = 222, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="% RH", MeasureMin = 0, MeasureMax = 100, AnalogMin = 0, AnalogMax = 1000, AnalogUnit = "0.1% RH", HardwareVersion= HardwareVersionEnum.通用撬装V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.环境大气压.ToString(), SerialNumber = 28, SensorType= SensorTypeEnum.模拟量传感器, SortId = 222, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 100, AnalogMin = 0, AnalogMax = 1000, AnalogUnit = "0.1kPa", HardwareVersion= HardwareVersionEnum.通用撬装V1}
            //// 终检V1
            //new ValveOperatorStationSensor(){ SensorName=CoilRegisterDeviceEnum.电磁阀1.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.开关量传感器, SortId = 1, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.电磁阀2.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.开关量传感器, SortId = 2, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.电磁阀3.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.开关量传感器, SortId = 3, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.电磁阀4.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.开关量传感器, SortId = 4, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.电磁阀5.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.开关量传感器, SortId = 5, HardwareVersion= HardwareVersionEnum.终检V1},

            //new(){ SensorName=CoilRegisterDeviceEnum.指标灯绿.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.开关量传感器, SortId = 6, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.指标灯黄.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.开关量传感器, SortId = 7, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.指标灯红.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.开关量传感器, SortId = 8, HardwareVersion= HardwareVersionEnum.终检V1},

            //new(){ SensorName=CoilRegisterDeviceEnum.流量计调零.ToString(), SerialNumber = 9, SensorType= SensorTypeEnum.开关量传感器, SortId = 9, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.激光调零.ToString(), SerialNumber = 10, SensorType= SensorTypeEnum.开关量传感器, SortId = 10, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.增量编码调零.ToString(), SerialNumber = 11, SensorType= SensorTypeEnum.开关量传感器, SortId = 11, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.绝对编码调零.ToString(), SerialNumber = 12, SensorType= SensorTypeEnum.开关量传感器, SortId = 12, HardwareVersion= HardwareVersionEnum.终检V1},

            //new(){ SensorName=DiscreteRegisterDeviceEnum.关位反馈.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.开关量传感器, SortId = 101, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=DiscreteRegisterDeviceEnum.开位反馈.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.开关量传感器, SortId = 102, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=DiscreteRegisterDeviceEnum.气泡检测.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.开关量传感器, SortId = 103, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=DiscreteRegisterDeviceEnum.工位启用.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.开关量传感器, SortId = 104, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=DiscreteRegisterDeviceEnum.急停开关.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.开关量传感器, SortId = 105, Inversion=true, HardwareVersion= HardwareVersionEnum.终检V1},

            //new(){ SensorName=HoldingRegisterDeviceEnum.定位器输出电流.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.数字量传感器, SortId = 301, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.气泡计数.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.模拟量传感器, SortId = 302, SensorConvertType = SensorConvertTypeEnum.Linear,MeasureUnit = "个", MeasureMin = 0, MeasureMax = 10000, AnalogUnit = "half", AnalogMin = 0, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.终检V1},

            //new(){ SensorName=InputRegisterDeviceEnum.环境温度.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.模拟量传感器, SortId = 201, SensorConvertType = SensorConvertTypeEnum.Ntc100K_3950, MeasureMin = -55, MeasureMax = 129, MeasureUnit="摄氏度" , AnalogMin = 0, AnalogMax = 4096, AnalogUnit = "adc", HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.定位器电流反馈.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.数字量传感器, SortId = 202,MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.阀位反馈.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.模拟量传感器, SortId = 203, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin =0 , MeasureMax = 100, AnalogUnit = "μA", AnalogMin = 4000, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.阀前压力.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.模拟量传感器, SortId = 204, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(表压)", MeasureMin =0 , MeasureMax = 1000, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.激光位移.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.模拟量传感器, SortId = 205, SensorConvertType = SensorConvertTypeEnum.Linear,MeasureUnit = "mm", MeasureMin = 0, MeasureMax = 250, AnalogUnit = "cmm(忽米)", AnalogMin = 0, AnalogMax = 25000 , HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.流量计1.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.模拟量传感器, SortId = 206, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="SCC", MeasureMin = 4, MeasureMax = 200, AnalogUnit = "百分之一SCC", AnalogMin = 400, AnalogMax=20000 , HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.流量计2.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.数字量传感器, SortId = 207, MeasureUnit="SCC", MeasureMin = 150, MeasureMax = 15000, AnalogUnit = "SCC", AnalogMin = 150, AnalogMax = 15000, HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.A口压力.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.模拟量传感器, SortId = 208, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000 , HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.B口压力.ToString(), SerialNumber = 9, SensorType= SensorTypeEnum.模拟量传感器, SortId = 209, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000 , HardwareVersion= HardwareVersionEnum.终检V1},
            //new(){ SensorName=InputRegisterDeviceEnum.气源压力.ToString(), SerialNumber = 10, SensorType= SensorTypeEnum.模拟量传感器, SortId = 210, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000 , HardwareVersion= HardwareVersionEnum.终检V1},

            //new(){ SensorName=InputRegisterDeviceEnum.增量编码器.ToString(), SerialNumber = 21, SensorType= SensorTypeEnum.模拟量传感器, SortId = 221, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mm", MeasureMin =0 , MeasureMax = 100, AnalogUnit = "脉冲数", AnalogMin =30000, AnalogMax = 40000 , HardwareVersion= HardwareVersionEnum.终检V1},


            //// 离线诊断V1
            //new ValveOperatorStationSensor(){ SensorName=CoilRegisterDeviceEnum.电磁阀1.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.开关量传感器, SortId = 1, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.电磁阀2.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.开关量传感器, SortId = 2, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=CoilRegisterDeviceEnum.电磁阀3.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.开关量传感器, SortId = 3, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=CoilRegisterDeviceEnum.电磁阀4.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.开关量传感器, SortId = 4, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=CoilRegisterDeviceEnum.电磁阀5.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.开关量传感器, SortId = 5, HardwareVersion= HardwareVersionEnum.离线诊断V1},

            ////new(){ SensorName=CoilRegisterDeviceEnum.指标灯绿.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.开关量传感器, SortId = 6, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=CoilRegisterDeviceEnum.指标灯黄.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.开关量传感器, SortId = 7, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=CoilRegisterDeviceEnum.指标灯红.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.开关量传感器, SortId = 8, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            
            //new(){ SensorName=CoilRegisterDeviceEnum.蜂鸣器.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.开关量传感器, SortId = 8, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.电源软开关.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.开关量传感器, SortId = 8, HardwareVersion= HardwareVersionEnum.离线诊断V1},

            ////new(){ SensorName=CoilRegisterDeviceEnum.流量计调零.ToString(), SerialNumber = 9, SensorType= SensorTypeEnum.开关量传感器, SortId = 9, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.激光调零.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.开关量传感器, SortId = 10, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.增量编码调零.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.开关量传感器, SortId = 11, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=CoilRegisterDeviceEnum.绝对编码调零.ToString(), SerialNumber = 12, SensorType= SensorTypeEnum.开关量传感器, SortId = 12, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.电流校准清零.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.开关量传感器, SortId = 12, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=CoilRegisterDeviceEnum.压力校准清零.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.开关量传感器, SortId = 13, HardwareVersion= HardwareVersionEnum.离线诊断V1},


            //new(){ SensorName=DiscreteRegisterDeviceEnum.关位反馈.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.开关量传感器, SortId = 101, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=DiscreteRegisterDeviceEnum.开位反馈.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.开关量传感器, SortId = 102, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=DiscreteRegisterDeviceEnum.充电中.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.开关量传感器, SortId = 103, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=DiscreteRegisterDeviceEnum.已充满.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.开关量传感器, SortId = 103, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=DiscreteRegisterDeviceEnum.气泡检测.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.开关量传感器, SortId = 103, HardwareVersion= HardwareVersionEnum.离线诊断V1},               //new(){ SensorName=DiscreteRegisterDeviceEnum.气泡检测.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.开关量传感器, SortId = 103, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=DiscreteRegisterDeviceEnum.工位启用.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.开关量传感器, SortId = 104, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=DiscreteRegisterDeviceEnum.急停开关.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.开关量传感器, SortId = 105, Inversion=true, HardwareVersion= HardwareVersionEnum.离线诊断V1},

            //new(){ SensorName=HoldingRegisterDeviceEnum.定位器输出电流.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.数字量传感器, SortId = 301, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            ////new(){ SensorName=HoldingRegisterDeviceEnum.气泡计数.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.模拟量传感器, SortId = 302, SensorConvertType = SensorConvertTypeEnum.Linear,MeasureUnit = "个", MeasureMin = 0, MeasureMax = 10000, AnalogUnit = "half", AnalogMin = 0, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.单位脉冲数.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.数字量传感器, SortId = 303, MeasureUnit="脉冲每秒", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.电流输出补偿.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.数字量传感器, SortId = 304, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},

            //new(){ SensorName=HoldingRegisterDeviceEnum.模拟量输入偏移量1.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.数字量传感器, SortId = 306, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.模拟量输入偏移量2.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.数字量传感器, SortId = 307, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.模拟量输入偏移量3.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.数字量传感器, SortId = 308, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.模拟量输入偏移量4.ToString(), SerialNumber = 9, SensorType= SensorTypeEnum.数字量传感器, SortId = 309, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},

            //new(){ SensorName=HoldingRegisterDeviceEnum.环境气压偏移量.ToString(), SerialNumber = 10, SensorType= SensorTypeEnum.模拟量传感器, SortId = 310, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.A口气压偏移量.ToString(), SerialNumber = 11, SensorType= SensorTypeEnum.模拟量传感器, SortId = 311, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.B口气压偏移量.ToString(), SerialNumber = 12, SensorType= SensorTypeEnum.模拟量传感器, SortId = 312, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=HoldingRegisterDeviceEnum.气源气压偏移量.ToString(), SerialNumber = 13, SensorType= SensorTypeEnum.模拟量传感器, SortId = 313, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},

            


            ////new(){ SensorName=InputRegisterDeviceEnum.环境温度.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.模拟量传感器, SortId = 201, SensorConvertType = SensorConvertTypeEnum.Ntc100K_3950, MeasureMin = -55, MeasureMax = 129, MeasureUnit="摄氏度" , AnalogMin = 0, AnalogMax = 4096, AnalogUnit = "adc", HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.电量指示.ToString(), SerialNumber = 1, SensorType= SensorTypeEnum.模拟量传感器, SortId = 201, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureMin = 0, MeasureMax = 100, MeasureUnit="%" , AnalogMin = 2456, AnalogMax = 3684, AnalogUnit = "adc", HardwareVersion= HardwareVersionEnum.离线诊断V1},

            //new(){ SensorName=InputRegisterDeviceEnum.定位器电流反馈.ToString(), SerialNumber = 2, SensorType= SensorTypeEnum.数字量传感器, SortId = 202,MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000,  HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.阀位反馈.ToString(), SerialNumber = 3, SensorType= SensorTypeEnum.模拟量传感器, SortId = 203, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="%", MeasureMin =0 , MeasureMax = 100, AnalogUnit = "μA", AnalogMin = 4000, AnalogMax = 20000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.流量计1.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.模拟量传感器, SortId = 206, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="SCC", MeasureMin = 4, MeasureMax = 200, AnalogUnit = "百分之一SCC", AnalogMin = 400, AnalogMax=20000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.流量计2.ToString(), SerialNumber = 5, SensorType= SensorTypeEnum.数字量传感器, SortId = 207, MeasureUnit="SCC", MeasureMin = 150, MeasureMax = 15000, AnalogUnit = "SCC", AnalogMin = 150, AnalogMax = 15000, HardwareVersion= HardwareVersionEnum.离线诊断V1},

            //new(){ SensorName=InputRegisterDeviceEnum.A口压力.ToString(), SerialNumber = 6, SensorType= SensorTypeEnum.模拟量传感器, SortId = 208, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.B口压力.ToString(), SerialNumber = 7, SensorType= SensorTypeEnum.模拟量传感器, SortId = 209, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.气源压力.ToString(), SerialNumber = 8, SensorType= SensorTypeEnum.模拟量传感器, SortId = 210, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.大气压力.ToString(), SerialNumber = 9, SensorType= SensorTypeEnum.模拟量传感器, SortId = 211, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(绝压)", MeasureMin =0 , MeasureMax = 1400, AnalogUnit = "mbar", AnalogMin =0, AnalogMax = 14000, HardwareVersion= HardwareVersionEnum.离线诊断V1},

            //new(){ SensorName=InputRegisterDeviceEnum.激光位移.ToString(), SerialNumber = 10, SensorType= SensorTypeEnum.模拟量传感器, SortId = 205, SensorConvertType = SensorConvertTypeEnum.Linear,MeasureUnit = "mm", MeasureMin = 0, MeasureMax = 250, AnalogUnit = "cmm(忽米)", AnalogMin = 0, AnalogMax = 25000, HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.高精度增量编码器.ToString(), SerialNumber = 11, SensorType= SensorTypeEnum.高精度模拟量传感器, SortId = 221, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mm", MeasureMin =0 , MeasureMax = 1, AnalogUnit = "脉冲数", AnalogMin =10000000, AnalogMax = 10000100, HardwareVersion= HardwareVersionEnum.离线诊断V1},

            //new(){ SensorName=InputRegisterDeviceEnum.模拟量输入1.ToString(), SerialNumber = 13, SensorType= SensorTypeEnum.数字量传感器, SortId = 213, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.模拟量输入2.ToString(), SerialNumber = 14, SensorType= SensorTypeEnum.数字量传感器, SortId = 214, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.模拟量输入3.ToString(), SerialNumber = 15, SensorType= SensorTypeEnum.数字量传感器, SortId = 215, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},
            //new(){ SensorName=InputRegisterDeviceEnum.模拟量输入4.ToString(), SerialNumber = 16, SensorType= SensorTypeEnum.数字量传感器, SortId = 216, MeasureUnit="μA", MeasureMin = 0, MeasureMax = 25000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},


            ////new(){ SensorName=InputRegisterDeviceEnum.阀前压力.ToString(), SerialNumber = 4, SensorType= SensorTypeEnum.模拟量传感器, SortId = 204, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="kPa(表压)", MeasureMin =0 , MeasureMax = 1000, AnalogUnit = "μA", AnalogMin =4000, AnalogMax = 20000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},

            ////new(){ SensorName=InputRegisterDeviceEnum.增量编码器.ToString(), SerialNumber = 21, SensorType= SensorTypeEnum.模拟量传感器, SortId = 221, SensorConvertType = SensorConvertTypeEnum.Linear, MeasureUnit="mm", MeasureMin =0 , MeasureMax = 100, AnalogUnit = "脉冲数", AnalogMin =30000, AnalogMax = 40000 , HardwareVersion= HardwareVersionEnum.离线诊断V1},

        ];
    }
}