namespace WoowApp.Services.Application;

/// <summary>
/// 系统角色表种子数据
/// </summary>
public partial class SysRoleSeedData : ISqlSugarEntitySeedData<SysRole>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysRole> HasData()
    {
        return new[]
        {
            new SysRole{ Id = 252885263003721, Name = "管理员", Code = "admin", SortId = 0, DataScope = (DataScopeEnum)2, Remark = "管理员", Status = (StatusEnum)1, TenantId = 100001 },
            new SysRole{ Id = 252885263003722, Name = "普通用户", Code = "common", SortId = 0, DataScope = (DataScopeEnum)4, Remark = "普通用户", Status = (StatusEnum)1, TenantId = 100001 },
            new SysRole{ Id = 252885263003723, Name = "游客", Code = "guest", SortId = 0, DataScope = (DataScopeEnum)5, Remark = "游客", Status = (StatusEnum)1, TenantId = 100001 },
            new SysRole{ Id = 645515515314821, Name = "出厂测试管理员", Code = "", SortId = 0, DataScope = (DataScopeEnum)4, Remark = "", Status = (StatusEnum)1 },

        };
    }
}