using Microsoft.Extensions.Logging;
using System.Net.WebSockets;

namespace WoowApp.Services.Application;

/// <summary>
/// 图表相关实时服务
/// </summary>
public class WsChartService : IScoped
{
    private readonly WsConext wsConext;
    private readonly ILogger<WsChartService> logger;
    private readonly InspectLogMananger<string> inspectLogMananger;
    //private readonly OperatorStationStatusMananger operatorStationStatusMananger;
    //private readonly SqlSugarRepository<ValveJob> jobRepo;
    //private readonly SqlSugarRepository<ValveJobLog> jobLogRepo;

    public WsChartService(
        WsConext wsConext,
        ILogger<WsChartService> logger,
        InspectLogMananger<string> inspectLogMananger
        //,
        //OperatorStationStatusMananger operatorStationStatusMananger,
        //SqlSugarRepository<ValveJob> jobRepo,
        //SqlSugarRepository<ValveJobLog> jobLogRepo
        )
    {
        this.wsConext = wsConext;
        this.logger = logger;
        this.inspectLogMananger = inspectLogMananger;
        //this.operatorStationStatusMananger = operatorStationStatusMananger;
        //this.jobRepo = jobRepo;
        //this.jobLogRepo = jobLogRepo;
    }

    private WebSocket WebSocket => wsConext.CurrentWebSocket ?? throw Oops.Oh("未找到WebSocket实例");

    /// <summary>
    /// 获取实时日志
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public async IAsyncEnumerable<string> GetLogs(string key)
    {
        await foreach (var item in inspectLogMananger.GetLogs(key))
        {
            yield return item.ToJson();
        }
    }



    ///// <summary>
    ///// 获取工位状态
    ///// </summary>
    ///// <param name="operatorStationId">工位内码</param>
    ///// <returns></returns>
    //public async IAsyncEnumerable<string> GetOperatorStationStatus(string operatorStationId)
    //{
    //    var job = await jobRepo.AsQueryable().Where(u => u.Status == ValveJobStatusEnum.测试中 && u.ValveOperatorStationId == Convert.ToInt64(operatorStationId)).FirstAsync();
    //    var lastJobId = job?.Id.ToString();
    //    yield return new GetOperatorStationStatusOutput() { OperatorStationId = operatorStationId, JobId = lastJobId, JobType = job?.JobType, StartTime = job?.StartTime }.ToJson();
    //    //await SendAsync(new GetOperatorStationStatusOutput() { OperatorStationId = operatorStationId, JobId = lastJobId, JobType = job?.JobType, StartTime = job?.StartTime }.ToJson());
    //    while (WebSocket.State == WebSocketState.Open)
    //    {
    //        // 查询当前任务
    //        var status = operatorStationStatusMananger.GetStatus(operatorStationId);
    //        if (status != null && (lastJobId == null || lastJobId != status.JobId))
    //        {
    //            lastJobId = status.JobId;
    //            yield return status.ToJson();
    //            //await SendAsync(status.ToJson());

    //        }
    //        else
    //        {
    //            await Task.Delay(200);
    //        }
    //    }
    //}
}
