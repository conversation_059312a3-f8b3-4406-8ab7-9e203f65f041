using System.Collections.Concurrent;

namespace WoowApp.Services.Application;

public class WsClientContainer : ISingleton
{
    private ConcurrentDictionary<string, WsClientService> ClientDict { get; set; } = [];

    public void AddClient(string connectionId, WsClientService value)
    {
        ClientDict[connectionId] = value;
    }

    /// <summary>
    /// 删除指定客户端
    /// </summary>
    public WsClientService TryRemoveClient(string connectionId)
    {
        ClientDict.TryRemove(connectionId, out var client);
        return client;
    }

    public WsClientService GetClient(string connectionId)
    {
        if (ClientDict.TryGetValue(connectionId, out var client))
        {
            return client;
        }
        throw Oops.Oh("未找到客户端");
    }

}
