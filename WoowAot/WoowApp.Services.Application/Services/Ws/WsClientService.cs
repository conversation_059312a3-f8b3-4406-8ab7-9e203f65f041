using System.Net.WebSockets;
using System.Text;
using System.Threading;
using Microsoft.Extensions.Logging;
using WoowApp.DataEncryption;
using WoowApp.Services.Application.SeedData;

namespace WoowApp.Services.Application;

/// <summary>
/// Ws客户端服务
/// </summary>
public class WsClientService : IScoped, IDisposable
{
    //private readonly IHttpContextAccessor httpContextAccessor;
    private readonly WsClientContainer wsClientContainer;
    private readonly WsConext wsConext;
    private readonly TransferManager<string> transferManager;
    //private readonly SqlSugarRepository<ValveOperatorStation> operatorStationRepo;
    private readonly ClientContainer container;
    private SemaphoreSlim semaphore = new SemaphoreSlim(1, 1);
    private bool IsLogin = false;
    protected readonly HttpContext httpContext;
    private readonly ILogger<WsClientService> logger;

    public WsClientService(
        IHttpContextAccessor httpContextAccessor,
        WsClientContainer wsClientContainer,
        WsConext wsConext,
        TransferManager<string> transferManager,
        //SqlSugarRepository<ValveOperatorStation> operatorStationRepo,
        ClientContainer container, ILogger<WsClientService> logger)
    {
        ConnectionId = $"ws-{Guid.NewGuid():N}";
        this.wsConext = wsConext;
        //this.httpContextAccessor = httpContextAccessor;
        this.wsClientContainer = wsClientContainer;
        this.transferManager = transferManager;
        //this.operatorStationRepo = operatorStationRepo;
        this.container = container;
        this.logger = logger;
        this.httpContext = httpContextAccessor.HttpContext!;
    }

    public string ConnectionId { get; private set; }
    protected string ClientId { get; set; }

    /// <summary>
    /// 接收WebSocket
    /// </summary>
    /// <returns></returns>
    public async Task<WebSocket> AcceptWebSocketAsync()
    {
        var clientKey = httpContext.Request.Headers["client"].ToString();
        var pwd = httpContext.Request.Headers["pwd"].ToString();
        var operatorStation = new ValveOperatorStationSeedData().HasData().First();
        //var operatorStation = await operatorStationRepo.AsQueryable().Where(u => u.AppKey == clientKey).FirstAsync();
        if (operatorStation == null || MD5Encryption.Encrypt(operatorStation.Password) != pwd && operatorStation.Password != pwd)
        {
            //await Clients.Caller.SendAsync("LoginFail");httpContext
            throw Oops.Oh("LoginFail");
        }
        var websocket = await httpContext.WebSockets.AcceptWebSocketAsync();
        wsConext.CurrentWebSocket = websocket;
        wsClientContainer.AddClient(ConnectionId, this);
        if (!string.IsNullOrEmpty(clientKey))
        {
            container.AddClient(clientKey, ConnectionId);
        }
        await SendDataAsync(new() { MsgType = "Login", Action = "Login", Key = operatorStation.Id.ToString(), InputJson = operatorStation.Name });
        IsLogin = true;
        return websocket;
    }

    /// <summary>
    /// 设置WebSocket
    /// </summary>
    /// <param name="webSocket"></param>
    public void SetCurrentWebSocket(WebSocket webSocket)
    {
        wsConext.CurrentWebSocket = webSocket;
    }

    protected WebSocket WebSocket => wsConext.CurrentWebSocket ?? throw Oops.Oh("未找到WebSocket实例");

    /// <summary>
    /// 当前状态
    /// </summary>
    public WebSocketState State => WebSocket.State;


    public async Task HandleWebSocket()
    {
        try
        {
            var buffer = new byte[8192];
            var receiveResult = await WebSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
            while (!receiveResult.CloseStatus.HasValue)
            {
                // logger.LogInformation("收到ws消息");
                // 处理接收到的消息
                semaphore.Release();
                var messageStr = Encoding.UTF8.GetString(buffer, 0, receiveResult.Count);
                var message = messageStr.ToObject<WsClientOutput>();
                var transfer = transferManager.GetTransfer(message.Key);
                transfer.Tcs.SetResult(message.ResultJson);
                // logger.LogInformation("收到ws消息-处理完毕");
                receiveResult = await WebSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
            }
            //await WebSocket.CloseAsync(receiveResult.CloseStatus.Value, receiveResult.CloseStatusDescription, CancellationToken.None);
        }
        catch (WebSocketException ex)
        {
            Console.WriteLine(ex.ToString());
        }
        finally
        {
            Dispose();
        }
        //});
    }


    /// <summary>
    /// 发送信息
    /// </summary>
    /// <returns></returns>
    protected async Task SendAsync(string msg)
    {
        await SendAsync(Encoding.UTF8.GetBytes(msg), WebSocketMessageType.Text, true, CancellationToken.None);
    }

    /// <summary>
    /// 发送信息
    /// </summary>
    /// <param name="buffer"></param>
    /// <param name="messageType"></param>
    /// <param name="endOfMessage"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    protected async Task SendAsync(ArraySegment<byte> buffer,
            WebSocketMessageType messageType,
            bool endOfMessage,
            CancellationToken cancellationToken)
    {
        if (IsLogin) await semaphore.WaitAsync(cancellationToken);
        // logger.LogInformation("发送ws消息");
        await WebSocket.SendAsync(buffer, messageType, endOfMessage, cancellationToken);
    }

    /// <summary>
    /// 关闭WebSocket
    /// </summary>
    /// <param name="closeStatus"></param>
    /// <param name="statusDescription"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    protected async Task CloseAsync(WebSocketCloseStatus closeStatus, string? statusDescription, CancellationToken cancellationToken)
    {
        await WebSocket.CloseAsync(closeStatus, statusDescription, cancellationToken);
    }

    ///// <summary>
    ///// 发送数据
    ///// </summary>
    ///// <param name="data"></param>
    ///// <returns></returns>
    //public async Task SendDataAsync(IAsyncEnumerable<string> data)
    //{
    //    await foreach (var msg in data)
    //    {
    //        await SendAsync(msg);
    //    }
    //}

    /// <summary>
    /// 发送信息
    /// </summary>
    /// <returns></returns>
    public async Task SendDataAsync(WsClientInput input)
    {
        await SendAsync(input.ToJson());
    }

    /// <summary>
    /// 销毁资源
    /// </summary>
    public void Dispose()
    {
        WebSocket?.Dispose();
        container.DeleteClient(ConnectionId);
        wsClientContainer.TryRemoveClient(ConnectionId);
    }
}
