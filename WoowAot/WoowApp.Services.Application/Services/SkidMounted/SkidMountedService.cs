using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using MiniExcelLibs;
using WoowApp.Services.Shared.Base;

namespace WoowApp.Services.Application;

/// <summary>
/// 撬装后台服务
/// </summary>
public class SkidMountedService : BackgroundService
{
    private readonly IServiceProvider serviceProvider;

    public SkidMountedService(IServiceProvider serviceProvider)
    {
        this.serviceProvider = serviceProvider;
    }


    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            using var scope = serviceProvider.CreateScope();
            Woow.SetServiceFactory(scope.ServiceProvider.GetService<IServiceFactory>()!);
            UnitOfWorkAttribute.SetServiceProvider(scope.ServiceProvider);
            if (!Woow.Configuration.GetSection("AppSettings").GetValue("RunSkidMounted", false))
            {
                return;
            }
            var skidMountedManager = scope.ServiceProvider.GetRequiredService<SkidMountedManager>();
            await skidMountedManager.ExecuteAsync(stoppingToken);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }
}
