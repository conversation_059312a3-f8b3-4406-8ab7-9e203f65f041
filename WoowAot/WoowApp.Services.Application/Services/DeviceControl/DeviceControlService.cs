using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;

namespace WoowApp.Services.Application.Services;

/// <summary>
/// 设备控制服务
/// </summary>
[ApiDescriptionSettings(AppConst.GroupName, Name = "设备控制服务", Order = 150)]
[AllowAnonymous]
public class DeviceControlService : IDynamicApiController, ITransient
{
    private readonly ServiceContext serviceContext;
    private readonly ControlDeviceManager controlDevice;
    private readonly ControlParams controlParams;
    private readonly IMemoryCache memoryCache;

    /// <summary>
    /// 设备控制服务
    /// </summary>
    /// <param name="serviceContext"></param>
    /// <param name="controlDevice"></param>
    /// <param name="controlParams"></param>
    public DeviceControlService(
        ServiceContext serviceContext,
        ControlDeviceManager controlDevice,
        ControlParams controlParams, IMemoryCache memoryCache)
    {
        this.serviceContext = serviceContext;
        this.controlDevice = controlDevice;
        this.controlParams = controlParams;
        this.memoryCache = memoryCache;
    }

    /// <summary>
    /// 获取控制参数
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "获取控制参数")]
    [OperationId("getControlParams"), HttpPost("/DeviceControl/getControlParams")]
    [NotLog]
    public async Task<DeviceControlSetControlParamsOutput> GetControlParamsAsync()
    {
        //var result = controlParams.Adapt<DeviceControlSetControlParamsOutput>();
        var result = controlParams.Mapper();
        return await Task.FromResult(result);

    }

    /// <summary>
    /// 设置控制参数
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "设置控制参数")]
    [OperationId("setControlParams"), HttpPost("/DeviceControl/setControlParams")]
    public async Task SetControlParamsAsync(DeviceControlSetControlParamsInput input)
    {
        if (input.Mode == ControlModeEnum.AutoStablePressureDrop && input.SetPoint > 300)
        {
            throw Oops.Oh("压差不能设置超过300kPa!");
        }

        controlParams.Mode = input.Mode;
        controlParams.SetPoint = input.SetPoint;
        controlParams.Duty = input.Duty;
        controlParams.Kp = input.Kp;
        controlParams.Ki = input.Ki;
        controlParams.Kd = input.Kd;

        await Task.CompletedTask;
    }

    /// <summary>
    /// 设置控制信号
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "设置控制信号")]
    [OperationId("setControlSignal"), HttpPost("/DeviceControl/setControlSignal")]
    public async Task SetControlSignalAsync(DeviceControlSetControlSignalInput input)
    {
        await SetCurrentClient(input.OperatorStationId);
        await controlDevice.SetControlSignalAsync(input.Device, input.Value);
    }

    /// <summary>
    /// 获取电磁阀状态
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "获取电磁阀状态")]
    [NotLog]
    [OperationId("getSolenoidValveState"), HttpPost("/DeviceControl/getSolenoidValveState")]
    public async Task<DeviceControlGetSolenoidValveStateOutput> GetSolenoidValveStateAsync(DeviceControlGetSolenoidValveStateInput input)
    {
        await SetCurrentClient(input.OperatorStationId);
        DeviceControlGetSolenoidValveStateOutput output = new();
        foreach (var device in input.DeviceList)
        {
            output.Data.Add(new() { Device = device, Status = await controlDevice.GetSolenoidValveStateAsync(device) });
        }
        return output;

    }

    /// <summary>
    /// 设置电磁阀状态
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "设置电磁阀状态")]
    [OperationId("setSolenoidValveState"), HttpPost("/DeviceControl/setSolenoidValveState")]
    public async Task SetSolenoidValveStateAsync(DeviceControlSetSolenoidValveStateInput input)
    {
        await SetCurrentClient(input.OperatorStationId);
        await controlDevice.SetSolenoidValveStateAsync(input.Device, input.Status);
    }


    /// <summary>
    /// 获取灯状态
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "获取灯状态")]
    [NotLog]
    [OperationId("getLampState"), HttpPost("/DeviceControl/getLampState")]
    public async Task<DeviceControlGetLampStateOutput> GetLampStateAsync(DeviceControlGetLampStateInput input)
    {
        await SetCurrentClient(input.OperatorStationId);
        DeviceControlGetLampStateOutput output = new();
        foreach (var device in input.DeviceList)
        {
            output.Data.Add(new() { Device = device, Status = await controlDevice.GetLampStateAsync(device) });
        }
        return output;
    }

    /// <summary>
    /// 设置灯状态
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "设置灯状态")]
    [OperationId("setLampState"), HttpPost("/DeviceControl/setLampState")]
    public async Task SetLampStateAsync(DeviceControlSetLampStateInput input)
    {
        await SetCurrentClient(input.OperatorStationId);
        await controlDevice.SetLampStateAsync(input.Device, input.Status);
    }

    /// <summary>
    /// 获取蜂鸣器状态
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "获取蜂鸣器状态")]
    [NotLog]
    [OperationId("getBuzzerState"), HttpPost("/DeviceControl/getBuzzerState")]
    public async Task<DeviceControlGetBuzzerStateOutput> GetBuzzerStateAsync(DeviceControlGetBuzzerStateInput input)
    {
        await SetCurrentClient(input.OperatorStationId);
        DeviceControlGetBuzzerStateOutput output = new();
        foreach (var device in input.DeviceList)
        {
            output.Data.Add(new() { Device = device, Status = await controlDevice.GetBuzzerStateAsync(device) });
        }
        return output;
    }

    /// <summary>
    /// 设置蜂鸣器状态
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "设置蜂鸣器状态")]
    [OperationId("setBuzzerState"), HttpPost("/DeviceControl/setBuzzerState")]
    public async Task SetBuzzerStateAsync(DeviceControlSetBuzzerStateInput input)
    {
        await SetCurrentClient(input.OperatorStationId);
        await controlDevice.SetBuzzerStateAsync(input.Device, input.Status);
    }

    /// <summary>
    /// 获取传感器信息（仪表盘）
    /// </summary>
    /// <returns></returns>
    [SwaggerOperation(Summary = "获取传感器信息（仪表盘）")]
    [NotLog]
    [OperationId("collect"), HttpPost("/DeviceControl/collect")]
    public async Task<DeviceControlCollectOutput> CollectAsync(DeviceControlCollectInput input)
    {
        var (_, operatorStation) = await SetCurrentClient(Convert.ToInt64(AppConst.JobId));

        DeviceControlCollectOutput result = new();

        result.PressureDropTarget = controlParams.Mode == ControlModeEnum.AutoStablePressureDrop ? (decimal)controlParams.SetPoint : null;
        result.ValvePrePressureTarget = controlParams.Mode == ControlModeEnum.AutoStabilizingPressure ? (decimal)controlParams.SetPoint : null;

        result.ValvePrePressure = Round3(await controlDevice.GetPressureInFrontOfValveAsync(true));
        result.PressureDrop = Round3(await controlDevice.GetPressureDropAsync());
        result.Temperature = Round3(await controlDevice.GetTemperatureAsync());
        result.LiquidLevel = Round3(await controlDevice.GetLiquidLevelAsync());
        result.Flowmeter = Round3(await controlDevice.GetFlowmeterAsync());
        result.VariableFrequencyDriveSpeed = Round3(await controlDevice.GetVariableFrequencyDriveSpeedAsync());
        result.InverterCurrent = Round3(await controlDevice.GetInverterCurrentAsync());
        result.ElectricSwitchValvePositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.电动开关阀阀位反馈));
        result.Valve1PositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.气动调节阀1阀位反馈));
        result.Valve2PositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.气动调节阀2阀位反馈));
        result.Valve3PositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.气动调节阀3阀位反馈));

        result.InverterFault = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器故障);
        result.InverterOperation = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器运行);
        result.InverterReady = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器就绪);
        result.InverterAlarm = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器报警);
        result.ElectricSwitchValveOpenPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.电动开关阀开位);
        result.ElectricSwitchValveClosedPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.电动开关阀关位);
        result.PneumaticSwitchValve1OpenPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀1开位);
        result.PneumaticSwitchValve1ClosedPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀1关位);
        result.PneumaticSwitchValve2OpenPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀2开位);
        result.PneumaticSwitchValve2ClosedPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀2关位);

        return result;
    }

    ///// <summary>
    ///// 设置定位器电流
    ///// </summary>
    ///// <returns></returns>
    //[SwaggerOperation(Summary = "设置定位器电流")]
    //[OperationId("setCurrentValue"), HttpPost("/DeviceControl/setCurrentValue")]
    //public async Task SetCurrentValueAsync(DeviceControlSetCurrentValueInput input)
    //{
    //    await SetCurrentClient(input.OperatorStationId);
    //    await controlDevice.SetInputCurrentAsync(input.CurrentValue);
    //}

    ///// <summary>
    ///// 设置比例阀1压力
    ///// </summary>
    ///// <returns></returns>
    //[SwaggerOperation(Summary = "设置比例阀1压力")]
    //[OperationId("setPressure1"), HttpPost("/DeviceControl/setPressure1")]
    //public async Task SetPressure1Async(DeviceControlSetCurrentValueInput input)
    //{
    //    await SetCurrentClient(input.OperatorStationId);
    //    await controlDevice.SetPressureAsync(HoldingRegisterDeviceEnum.比例阀1压力, input.CurrentValue);
    //}

    ///// <summary>
    ///// 设置比例阀2压力
    ///// </summary>
    ///// <returns></returns>
    //[SwaggerOperation(Summary = "设置比例阀2压力")]
    //[OperationId("setPressure2"), HttpPost("/DeviceControl/setPressure2")]
    //public async Task SetPressure2Async(DeviceControlSetCurrentValueInput input)
    //{
    //    await SetCurrentClient(input.OperatorStationId);
    //    await controlDevice.SetPressureAsync(HoldingRegisterDeviceEnum.比例阀2压力, input.CurrentValue);
    //}

    ///// <summary>
    ///// 传感器调零
    ///// </summary>
    ///// <returns></returns>
    //[SwaggerOperation(Summary = "传感器调零")]
    //[OperationId("setZero"), HttpPost("/DeviceControl/setZero")]
    //public async Task SetZeroAsync(DeviceControlSetZeroInput input)
    //{
    //    await SetCurrentClient(input.OperatorStationId);
    //    await controlDevice.SetZeroAsync(input.Device);
    //}

    ///// <summary>
    ///// 传感器修正
    ///// </summary>
    ///// <returns></returns>
    //[SwaggerOperation(Summary = "传感器修正")]
    //[OperationId("correct"), HttpPost("/DeviceControl/correct")]
    //public async Task CorrectAsync(DeviceControlCorrectInput input)
    //{
    //    await SetCurrentClient(input.OperatorStationId);
    //    await controlDevice.CorrectAsync(input.Device, input.Value);
    //}

    /// <summary>
    /// 保留3位小数
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    private decimal Round3(decimal value) => decimal.Round(value, 3);

    /// <summary>
    /// 设置当前客户端
    /// </summary>
    /// <param name="operatorStationId">工位</param>
    /// <returns></returns>
    private async Task<(string, ValveOperatorStation)> SetCurrentClient(long operatorStationId)
    {
        var client = serviceContext.CurrentClient;
        var operatorStation = client.OperatorStation;
        await Task.CompletedTask;
        return (client.Key, operatorStation);
    }

    /// <summary>
    /// 自动采集设置是否暂停
    /// </summary>
    [SwaggerOperation(Summary = "自动采集设置是否暂停")]
    [NotLog]
    [OperationId("setPause"), HttpPost("/DeviceControl/setPause")]
    public async Task SetPauseAsync(bool pause)
    {
        memoryCache.Set("pause", pause);
        await Task.CompletedTask;
    }
}
