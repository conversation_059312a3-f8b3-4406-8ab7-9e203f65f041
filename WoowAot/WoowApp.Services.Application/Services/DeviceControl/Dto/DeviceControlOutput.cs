namespace WoowApp.Services.Application.Service.Dto;

/// <summary>
/// 获取电磁阀状态输出参数
/// </summary>
public class DeviceControlGetSolenoidValveStateOutput
{
    /// <summary>
    /// 电磁阀状态数据
    /// </summary>
    public List<DeviceControlGetSolenoidValveStateOutputItem> Data { get; set; } = [];
}

/// <summary>
/// 获取电磁阀状态项输出参数
/// </summary>
public class DeviceControlGetSolenoidValveStateOutputItem
{

    /// <summary>
    /// 电磁阀设备
    /// </summary>
    [ExcelColumn(Name = "电磁阀设备")]
    [Display(Name = "电磁阀设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public CoilRegisterDeviceEnum Device { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public SolenoidValveStatusEnum Status { get; set; }
}


/// <summary>
/// 获取灯状态输出参数
/// </summary>
public class DeviceControlGetLampStateOutput
{
    /// <summary>
    /// 灯状态数据
    /// </summary>
    public List<DeviceControlGetLampStateOutputItem> Data { get; set; } = [];

}
/// <summary>
/// 获取灯状态输出参数
/// </summary>
public class DeviceControlGetLampStateOutputItem
{

    /// <summary>
    /// 灯设备
    /// </summary>
    [ExcelColumn(Name = "灯设备")]
    [Display(Name = "灯设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public CoilRegisterDeviceEnum Device { get; set; }

    /// <summary>
    /// 灯状态
    /// </summary>
    public LampStatusEnum Status { get; set; }
}

/// <summary>
/// 获取蜂鸣器状态输出参数
/// </summary>
public class DeviceControlGetBuzzerStateOutput
{
    /// <summary>
    /// 蜂鸣器状态数据
    /// </summary>
    public List<DeviceControlGetBuzzerStateOutputItem> Data { get; set; } = [];

    /// <summary>
    /// 日志列表结构（服务前端用，当前未赋值）
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public List<LogData<string>> LogList { get; set; }

    /// <summary>
    /// 传感器数据采集信息（服务前端用，当前未赋值）
    /// </summary>
    [ExcelColumn(Ignore = true)]
    public List<SensorDataOutput> SensorInfoList { get; set; }

}
/// <summary>
/// 获取灯状态输出参数
/// </summary>
public class DeviceControlGetBuzzerStateOutputItem
{

    /// <summary>
    /// 蜂鸣器设备
    /// </summary>
    [ExcelColumn(Name = "蜂鸣器设备")]
    [Display(Name = "蜂鸣器设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public CoilRegisterDeviceEnum Device { get; set; }

    /// <summary>
    /// 蜂鸣器状态
    /// </summary>
    public BuzzerStatusEnum Status { get; set; }
}

/// <summary>
/// 传感器信息采集
/// </summary>
public class DeviceControlCollectOutput
{

    /// <summary>
    /// 阀前压力目标值
    /// </summary>
    public decimal? ValvePrePressureTarget { get; set; }

    /// <summary>
    /// 阀前压差目标值
    /// </summary>
    public decimal? PressureDropTarget { get; set; }

    /// <summary>
    /// 阀前压力
    /// </summary>
    public decimal? ValvePrePressure { get; set; }

    /// <summary>
    /// 阀前压差
    /// </summary>
    public decimal? PressureDrop { get; set; }

    /// <summary>
    /// 温度
    /// </summary>
    public decimal? Temperature { get; set; }

    /// <summary>
    /// 液位
    /// </summary>
    public decimal? LiquidLevel { get; set; }

    /// <summary>
    /// 流量计
    /// </summary>
    public decimal? Flowmeter { get; set; }

    /// <summary>
    /// 变频器转速
    /// </summary>
    public decimal? VariableFrequencyDriveSpeed { get; set; }

    /// <summary>
    /// 变频器电流
    /// </summary>
    public decimal? InverterCurrent { get; set; }

    ///// <summary>
    ///// 湿度
    ///// </summary>
    //public decimal? Humidity { get; set; }

    /// <summary>
    /// 电动开关阀阀位反馈
    /// </summary>
    public decimal? ElectricSwitchValvePositionFeedback { get; set; }

    /// <summary>
    /// 气动调节阀1阀位反馈
    /// </summary>
    public decimal? Valve1PositionFeedback { get; set; }

    /// <summary>
    /// 气动调节阀2阀位反馈
    /// </summary>
    public decimal? Valve2PositionFeedback { get; set; }

    /// <summary>
    /// 气动调节阀3阀位反馈
    /// </summary>
    public decimal? Valve3PositionFeedback { get; set; }

    /// <summary>
    /// 变频器故障
    /// </summary>
    public bool? InverterFault { get; set; }

    /// <summary>
    /// 变频器运行
    /// </summary>
    public bool? InverterOperation { get; set; }

    /// <summary>
    /// 变频器就绪
    /// </summary>
    public bool? InverterReady { get; set; }

    /// <summary>
    /// 变频器报警
    /// </summary>
    public bool? InverterAlarm { get; set; }

    /// <summary>
    /// 电动开关阀开位
    /// </summary>
    public bool? ElectricSwitchValveOpenPosition { get; set; }

    /// <summary>
    /// 电动开关阀关位
    /// </summary>
    public bool? ElectricSwitchValveClosedPosition { get; set; }

    /// <summary>
    /// 气动开关阀1开位
    /// </summary>
    public bool? PneumaticSwitchValve1OpenPosition { get; set; }

    /// <summary>
    /// 气动开关阀1关位
    /// </summary>
    public bool? PneumaticSwitchValve1ClosedPosition { get; set; }

    /// <summary>
    /// 气动开关阀2开位
    /// </summary>
    public bool? PneumaticSwitchValve2OpenPosition { get; set; }

    /// <summary>
    /// 气动开关阀2关位
    /// </summary>
    public bool? PneumaticSwitchValve2ClosedPosition { get; set; }

}

/// <summary>
/// 控制参数 输出
/// </summary>
public class DeviceControlSetControlParamsOutput : DeviceControlSetControlParamsInput;