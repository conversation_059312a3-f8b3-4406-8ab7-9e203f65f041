using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application;

 
/// <summary>
/// 对象映射转换
/// </summary>
[Riok.Mapperly.Abstractions.Mapper]
public static partial class DeviceControlMapper
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public static partial DeviceControlSetControlParamsOutput Mapper(this ControlParams input);
}
