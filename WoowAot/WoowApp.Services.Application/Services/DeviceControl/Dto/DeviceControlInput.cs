namespace WoowApp.Services.Application.Service.Dto;

/// <summary>
/// 获取电磁阀状态输入参数
/// </summary>
public class DeviceControlGetSolenoidValveStateInput
{


    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 电磁阀设备
    /// </summary>
    [ExcelColumn(Name = "电磁阀设备")]
    [Display(Name = "电磁阀设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public List<CoilRegisterDeviceEnum> DeviceList { get; set; }

}

/// <summary>
/// 设置电磁阀状态输入参数
/// </summary>
public class DeviceControlSetSolenoidValveStateInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 电磁阀设备
    /// </summary>
    [ExcelColumn(Name = "电磁阀设备")]
    [Display(Name = "电磁阀设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public CoilRegisterDeviceEnum Device { get; set; }


    /// <summary>
    /// 电磁阀状态
    /// </summary>
    [ExcelColumn(Name = "电磁阀状态")]
    [Display(Name = "电磁阀状态")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public SolenoidValveStatusEnum Status { get; set; }

}

/// <summary>
/// 设置控制信号输入参数
/// </summary>
public class DeviceControlSetControlSignalInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 设备
    /// </summary>
    [ExcelColumn(Name = "设备")]
    [Display(Name = "设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public HoldingRegisterDeviceEnum Device { get; set; }


    /// <summary>
    /// 信号值
    /// </summary>
    [ExcelColumn(Name = "信号值")]
    [Display(Name = "信号值")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public decimal Value { get; set; }

}

/// <summary>
/// 设置控制参数输入参数
/// </summary>
public class DeviceControlSetControlParamsInput
{

    /// <summary>
    /// 控制模式
    /// </summary>
    [Display(Name = "控制模式")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public ControlModeEnum Mode { get; set; }

    /// <summary>
    /// 目标值
    /// </summary>
    [Display(Name = "目标值")]
    [Range(0, 1000)]
    public float SetPoint { get; set; }

    /// <summary>
    /// 控制周期(几次采样控制一次)
    /// </summary>
    [Display(Name = "控制周期")]
    [Range(1, 100)]
    public int Duty { get; set; } = 3;

    /// <summary>
    /// 比例系数
    /// </summary>
    [Display(Name = "比例系数")]
    [Range(0, 100)]
    public float Kp { get; set; } = 3.0f;

    /// <summary>
    /// 积分系数
    /// </summary>
    [Display(Name = "积分系数")]
    [Range(0, 100)]
    public float Ki { get; set; } = 0.1f;

    /// <summary>
    /// 微分系数
    /// </summary>
    [Display(Name = "微分系数")]
    [Range(0, 100)]
    public float Kd { get; set; } = 5.0f;

}

/// <summary>
/// 获取灯状态输入参数
/// </summary>
public class DeviceControlGetLampStateInput
{


    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 灯设备
    /// </summary>
    [ExcelColumn(Name = "灯设备")]
    [Display(Name = "灯设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public List<CoilRegisterDeviceEnum> DeviceList { get; set; }

}

/// <summary>
/// 设置灯状态输入参数
/// </summary>
public class DeviceControlSetLampStateInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 电磁阀设备
    /// </summary>
    [ExcelColumn(Name = "电磁阀设备")]
    [Display(Name = "电磁阀设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public CoilRegisterDeviceEnum Device { get; set; }


    /// <summary>
    /// 灯状态
    /// </summary>
    [ExcelColumn(Name = "灯状态")]
    [Display(Name = "灯状态")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public LampStatusEnum Status { get; set; }

}
/// <summary>
/// 获取蜂鸣器状态输入参数
/// </summary>
public class DeviceControlGetBuzzerStateInput
{


    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 蜂鸣器设备
    /// </summary>
    [ExcelColumn(Name = "蜂鸣器设备")]
    [Display(Name = "蜂鸣器设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public List<CoilRegisterDeviceEnum> DeviceList { get; set; }

}

/// <summary>
/// 设置蜂鸣器状态输入参数
/// </summary>
public class DeviceControlSetBuzzerStateInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 蜂鸣器设备
    /// </summary>
    [ExcelColumn(Name = "蜂鸣器设备")]
    [Display(Name = "蜂鸣器设备")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public CoilRegisterDeviceEnum Device { get; set; }


    /// <summary>
    /// 蜂鸣器状态
    /// </summary>
    [ExcelColumn(Name = "蜂鸣器状态")]
    [Display(Name = "蜂鸣器状态")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public BuzzerStatusEnum Status { get; set; }

}


/// <summary>
/// 获取串口信息输入参数
/// </summary>
public class DeviceControlGetSerialPortModelsInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

}

/// <summary>
/// 获取传感器信息参数
/// </summary>
public class DeviceControlCollectInput
{

    ///// <summary>
    ///// 工位
    ///// </summary>
    //[ExcelColumn(Name = "工位")]
    //[Display(Name = "工位")]
    //[Required(ErrorMessage = "{0} - 不能为空")]
    //[SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    //public virtual long OperatorStationId { get; set; }

    ///// <summary>
    ///// 行程类型
    ///// </summary>
    //[Display(Name = "行程类型")]
    //[Required(ErrorMessage = "{0} - 不能为空")]
    //public StrokeTypeEnum StrokeType { get; set; } = StrokeTypeEnum.直行程;

    ///// <summary>
    ///// 阀门连接件直径(单位mm)
    ///// </summary>
    //[Display(Name = "阀门连接件直径")]
    //public decimal? ConnectionDiameter { get; set; }

}

/// <summary>
/// 设置定位器电流输入参数
/// </summary>
public class DeviceControlSetCurrentValueInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 设置电流
    /// </summary>
    [ExcelColumn(Name = "设置电流")]
    [Display(Name = "设置电流")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [Range(0, 25000, ErrorMessage = "设置电流需要在0~25000之间")]
    public decimal CurrentValue { get; set; }

}

/// <summary>
/// 传感器调零输入参数
/// </summary>
public class DeviceControlSetZeroInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 传感器
    /// </summary>
    [ExcelColumn(Name = "传感器")]
    [Display(Name = "传感器")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public CoilRegisterDeviceEnum Device { get; set; }

}
/// <summary>
/// 传感器修正输入参数
/// </summary>
public class DeviceControlCorrectInput
{

    /// <summary>
    /// 工位
    /// </summary>
    [ExcelColumn(Name = "工位")]
    [Display(Name = "工位")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    [SnowflakeId, RegularExpression(@"^(?!0$)\d+$", ErrorMessage = "{0}不满足条件")]
    public virtual long OperatorStationId { get; set; }

    /// <summary>
    /// 传感器
    /// </summary>
    [ExcelColumn(Name = "传感器")]
    [Display(Name = "传感器")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public HoldingRegisterDeviceEnum Device { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    [ExcelColumn(Name = "值")]
    [Display(Name = "值")]
    [Required(ErrorMessage = "{0} - 不能为空")]
    public decimal Value { get; set; }

}