namespace WoowApp.Services.Application.Models;

public class ClientContainer: <PERSON>ingleton
{
    List<ClientConnection> Clients = new();

    public void AddClient(string holderId, string connectionId)
    {
        lock (this)
        {
            var item = Clients.Where(t => t.ConnectionId == connectionId).FirstOrDefault();
            if (item == null)
            {
                Clients.Add(new ClientConnection { ClientId = holderId, ConnectionId = connectionId });
            }
            else
            {
                item.ClientId = holderId;
            }
        }
    }

    public void DeleteClient(string connectionId)
    {
        lock (this)
        {
            Clients = Clients.Where(t => t.ConnectionId != connectionId).ToList();
        }
    }

    public List<string> GetConnectionIds(string holderId)
    {
        return Clients.Where(t => t.ClientId == holderId || holderId == "*").Select(t => t.ConnectionId).Distinct().ToList();
    }

    public string GetConnectionId(string holderId)
    {
        return Clients.Where(t => t.ClientId == holderId).Select(t => t.ConnectionId).LastOrDefault();
    }
}
