using WoowApp.Services.Shared.JsonSerialize;

namespace WoowApp.Services.Application.Models;

/// <summary>
/// 控制参数（外部）
/// </summary>
[JsonSerializeRegister]
public class ControlParams : ISingleton
{
    private ControlModeEnum mode = ControlModeEnum.Manual;

    /// <summary>
    /// 控制模式
    /// </summary>
    public ControlModeEnum Mode
    {
        get
        {
            return mode;
        }
        set
        {
            if (value != mode)
            {
                OnChangeMode?.Invoke(value, mode);
            }
            mode = value;
        }
    }

    /// <summary>
    /// 修改控制模式，参数1新模式，参数2旧模式
    /// </summary>
    [JsonIgnore]
    internal Action<ControlModeEnum, ControlModeEnum>? OnChangeMode { get; set; }

    /// <summary>
    /// 目标值
    /// </summary>
    [Range(0, 1000)]
    public float SetPoint { get; set; }

    /// <summary>
    /// 控制周期(几次采样控制一次)
    /// </summary>
    [Range(1, 100)]
    public int Duty { get; set; } = 3;

    /// <summary>
    /// 比例系数
    /// </summary>
    [Range(0, 100)]
    public float Kp { get; set; } = 3.0f;

    /// <summary>
    /// 积分系数
    /// </summary>
    [Range(0, 100)]
    public float Ki { get; set; } = 0.1f;

    /// <summary>
    /// 微分系数
    /// </summary>
    [Range(0, 100)]
    public float Kd { get; set; } = 5.0f;

}
