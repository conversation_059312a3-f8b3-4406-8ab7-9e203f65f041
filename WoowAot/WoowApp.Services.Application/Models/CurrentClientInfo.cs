using WoowApp.Services.Application.SeedData;
using WoowApp.Services.Shared.Base;

namespace WoowApp.Services.Application.Models;

/// <summary>
/// 当前设备检测信息
/// </summary>
public class CurrentClientInfo
{
    public CurrentClientInfo()
    {
        OperatorStation = new ValveOperatorStationSeedData().HasData().FirstOrDefault();
        IpAddress = Woow.Configuration["AppSettings:IpAddress"] ?? "127.0.0.1";
        var port = Woow.Configuration["AppSettings:Port"];
        Port = int.TryParse(port, out int val) ? val : 8001;
        Key = AppConst.JobId;
    }

    /// <summary>
    /// 设备Id
    /// </summary>
    public string? ClientId { get; set; } = AppConst.JobId;

    ///// <summary>
    ///// 连接Id
    ///// </summary>
    //public string? ConnectionId { get; set; }

    /// <summary>
    /// 关键信息
    /// </summary>
    public string Key { get; set; }

    ///// <summary>
    ///// 动作类型
    ///// </summary>
    //public string Action { get; set; }

    /// <summary>
    /// 当前检测步骤
    /// </summary>
    public string Step { get; set; }

    /// <summary>
    /// 阀门Id
    /// </summary>
    public string? ValveId { get; set; }

    /// <summary>
    /// 输入电流
    /// </summary>
    public decimal InputCurrent { get; set; }

    /// <summary>
    /// 目标行程（百分比）
    /// </summary>
    public decimal TargetPosition { get; set; }

    /// <summary>
    /// 上一目标行程（百分比）
    /// </summary>
    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public decimal? LastTargetPosition { get; set; }

    /// <summary>
    /// 上一实际行程（百分比）
    /// </summary>
    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public decimal? LastActualPosition { get; set; }

    /// <summary>
    /// 工位
    /// </summary>
    [SnowflakeId]
    public long? ValveOperatorStationId { get; set; }

    /// <summary>
    /// 自整定模式
    /// </summary>
    public bool IsSelfTune { get; set; }

    /// <summary>
    /// 双作用自动设置另一比例阀压力
    /// </summary>
    public bool AutoSetOtherPressure { get; set; } = true;

    /// <summary>
    /// 不允许修改实际压力
    /// </summary>
    public bool NotAllowChange { get; set; }

    /// <summary>
    /// 当前控制设备
    /// </summary>
    public HoldingRegisterDeviceEnum TargetDevice { get; set; } = HoldingRegisterDeviceEnum.气动调节阀2控制信号;

    /// <summary>
    /// 当前比例阀1压力
    /// </summary>
    public decimal Pressure1 { get; set; }

    /// <summary>
    /// 当前比例阀2压力
    /// </summary>
    public decimal Pressure2 { get; set; }


    /// <summary>
    /// Modbus tcp地址
    /// </summary>
    public string IpAddress { get; set; }


    /// <summary>
    /// Modbus 端口
    /// </summary>
    public int? Port { get; set; }

    /// <summary>
    /// 工位
    /// </summary>
    public ValveOperatorStation OperatorStation { get; set; }

    /// <summary>
    /// 当前设备数据
    /// </summary>
    public SensorDataOutput SensorData { get; set; } = new SensorDataOutput();
}
