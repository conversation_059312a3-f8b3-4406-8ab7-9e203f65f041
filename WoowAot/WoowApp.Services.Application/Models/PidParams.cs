namespace WoowApp.Services.Application.Models;

/// <summary>
/// PID控制参数
/// </summary>
public class PidParams
{
    /// <summary>
    /// 周期
    /// </summary>
    public float Duty { get; set; }
    /// <summary>
    /// 目标
    /// </summary>
    public float SetPoint { get; set; }
    /// <summary>
    /// 输入
    /// </summary>
    public float Input { get; set; }
    /// <summary>
    /// 比例系数
    /// </summary>
    public float Kp { get; set; }
    /// <summary>
    /// 积分系数
    /// </summary>
    public float Ki { get; set; }
    /// <summary>
    /// 微分系数
    /// </summary>
    public float Kd { get; set; }
    /// <summary>
    /// 积分
    /// </summary>
    public float Integral { get; set; }
    /// <summary>
    /// 最近一次误差
    /// </summary>
    public float LastError { get; set; }
    /// <summary>
    /// 输出
    /// </summary>
    public float Output { get; set; }
    /// <summary>
    /// 最小输出
    /// </summary>
    public float MinOutput { get; set; } = 0;
    /// <summary>
    /// 最大输出
    /// </summary>
    public float MaxOutput { get; set; } = 1;
}
