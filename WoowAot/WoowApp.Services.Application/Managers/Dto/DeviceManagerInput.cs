using System.ComponentModel;

namespace WoowApp.Services.Application.Managers.Dto;

/// <summary>
/// 获取设备值输入参数
/// </summary>
public class DeviceManagerGetValuesInput
{

    /// <summary>
    /// 从机地址
    /// </summary>
    public byte SlaveId { get; set; } = 254;

    /// <summary>
    /// 寄存器开始地址
    /// </summary>

    public ushort StartAddress { get; set; }

    /// <summary>
    /// 获取值个数
    /// </summary>
    [DefaultValue(2)]
    public ushort NumberOfPoints { get; set; }
}

/// <summary>
/// 设置设备值输入参数
/// </summary>
public class DeviceManagerSetValueInput<T>
{

    /// <summary>
    /// 从机地址
    /// </summary>
    public byte SlaveId { get; set; } = 254;

    /// <summary>
    /// 寄存器地址
    /// </summary>
    public ushort Address { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public T Value { get; set; }
}

/// <summary>
/// 设置串口信息输入参数
/// </summary>
public class DeviceManagerSetSerialPortInput
{

    /// <summary>
    /// 串口
    /// </summary>
    public string ComName { get; set; } = "COM3";

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; } = 115200;

}
