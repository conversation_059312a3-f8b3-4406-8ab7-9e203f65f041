using Microsoft.Extensions.Caching.Distributed;

namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 设备管理
/// </summary>
public class ControlDeviceManager : IScoped
{
    /// <summary>
    /// 设备管理
    /// </summary>
    /// <param name="deviceCacheManager"></param>
    /// <param name="sensorManager"></param>
    /// <param name="cache"></param>
    public ControlDeviceManager(
        DeviceCacheManager deviceCacheManager,
        SensorManager sensorManager, IDistributedCache cache)
    {
        this.deviceCacheManager = deviceCacheManager;
        this.sensorManager = sensorManager;
        this.cache = cache;
    }

    private readonly DeviceCacheManager deviceCacheManager;
    private readonly SensorManager sensorManager;
    private readonly IDistributedCache cache;


    /// <summary>
    /// 获取设备序号
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <returns></returns>
    private async Task<ushort> GetSerialNumber(string sensorName) => await sensorManager.GetSerialNumber(sensorName);

    /// <summary>
    /// 获取设备信号是否反转
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <returns></returns>
    private async Task<bool> GetInversion(string sensorName) => await sensorManager.GetInversion(sensorName);

    /// <summary>
    /// 数字量转模拟量值
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <param name="digitalValue">数字量值</param>
    /// <returns>模拟量值</returns>
    private async Task<ushort> ConvertAnalogValue(string sensorName, decimal digitalValue) => await sensorManager.ConvertAnalogValue(sensorName, digitalValue);

    /// <summary>
    /// 模拟量转数字量值
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <param name="analogValue">模拟量值</param>
    /// <returns>数字量值</returns>
    private async Task<decimal> ConvertDigitalValue(string sensorName, ushort analogValue) => await sensorManager.ConvertDigitalValue(sensorName, analogValue);


    ///// <summary>
    ///// 高精度模拟量转数字量值
    ///// </summary>
    ///// <param name="sensorName">传感器名称</param>
    ///// <param name="analogValue">模拟量值</param>
    ///// <returns>数字量值</returns>
    //private async Task<decimal> ConvertDigitalValueHighPrecision(string sensorName, uint analogValue) => await sensorManager.ConvertDigitalValueHighPrecision(sensorName, analogValue);

    /// <summary>
    /// 获取电磁阀状态
    /// </summary>
    /// <returns></returns>
    public async Task<SolenoidValveStatusEnum> GetSolenoidValveStateAsync(CoilRegisterDeviceEnum coilRegisterDevice, bool notAutoIgnore = false)
    {
        var values = await deviceCacheManager.ReadCoilRegisterAsync(notAutoIgnore);
        var serialNumber = await GetSerialNumber(coilRegisterDevice.ToString());
        var inversion = await GetInversion(coilRegisterDevice.ToString());
        return values[serialNumber % values.Length] == !inversion ? SolenoidValveStatusEnum.开启 : SolenoidValveStatusEnum.关闭;
    }

    /// <summary>
    /// 设置电磁阀状态
    /// </summary>
    /// <returns></returns>
    public async Task SetSolenoidValveStateAsync(CoilRegisterDeviceEnum coilRegisterDevice, SolenoidValveStatusEnum solenoidValveStatus)
    {
        var serialNumber = await GetSerialNumber(coilRegisterDevice.ToString());
        var inversion = await GetInversion(coilRegisterDevice.ToString());
        await deviceCacheManager.WriteSingleCoilAsync(new() { Value = solenoidValveStatus == SolenoidValveStatusEnum.开启 == !inversion, Address = serialNumber });
    }

    /// <summary>
    /// 获取LED灯状态
    /// </summary>
    /// <returns></returns>
    public async Task<LampStatusEnum> GetLampStateAsync(CoilRegisterDeviceEnum coilRegisterDevice, bool notAutoIgnore = false)
    {
        var values = await deviceCacheManager.ReadCoilRegisterAsync(notAutoIgnore);
        var serialNumber = await GetSerialNumber(coilRegisterDevice.ToString());
        var inversion = await GetInversion(coilRegisterDevice.ToString());
        return values[serialNumber % values.Length] == !inversion ? LampStatusEnum.亮 : LampStatusEnum.灭;
    }

    /// <summary>
    /// 设置LED灯状态
    /// </summary>
    /// <returns></returns>
    public async Task SetLampStateAsync(CoilRegisterDeviceEnum coilRegisterDevice, LampStatusEnum lampStatus)
    {
        var serialNumber = await GetSerialNumber(coilRegisterDevice.ToString());
        var inversion = await GetInversion(coilRegisterDevice.ToString());
        await deviceCacheManager.WriteSingleCoilAsync(new() { Value = lampStatus == LampStatusEnum.亮 == !inversion, Address = serialNumber });
    }

    /// <summary>
    /// 获取蜂鸣器状态
    /// </summary>
    /// <returns></returns>
    public async Task<BuzzerStatusEnum> GetBuzzerStateAsync(CoilRegisterDeviceEnum coilRegisterDevice, bool notAutoIgnore = false)
    {
        var values = await deviceCacheManager.ReadCoilRegisterAsync(notAutoIgnore);
        var serialNumber = await GetSerialNumber(coilRegisterDevice.ToString());
        var inversion = await GetInversion(coilRegisterDevice.ToString());
        return values[serialNumber % values.Length] == !inversion ? BuzzerStatusEnum.开 : BuzzerStatusEnum.关;
    }

    /// <summary>
    /// 设置蜂鸣器状态
    /// </summary>
    /// <returns></returns>
    public async Task SetBuzzerStateAsync(CoilRegisterDeviceEnum coilRegisterDevice, BuzzerStatusEnum buzzerStatus)
    {
        var serialNumber = await GetSerialNumber(coilRegisterDevice.ToString());
        var inversion = await GetInversion(coilRegisterDevice.ToString());
        await deviceCacheManager.WriteSingleCoilAsync(new() { Value = buzzerStatus == BuzzerStatusEnum.开 == !inversion, Address = serialNumber });
    }

    /// <summary>
    /// 传感器调零
    /// </summary>
    /// <returns></returns>
    public async Task SetZeroAsync(CoilRegisterDeviceEnum coilRegisterDevice)
    {
        var serialNumber = await GetSerialNumber(coilRegisterDevice.ToString());
        var inversion = await GetInversion(coilRegisterDevice.ToString());
        await deviceCacheManager.WriteSingleCoilAsync(new() { Value = !inversion, Address = serialNumber });
    }

    /// <summary>
    /// 获取开关反馈状态
    /// </summary>
    /// <returns></returns>
    public async Task<bool> GetFeedbackStateAsync(DiscreteRegisterDeviceEnum discreteRegisterDevice, bool notAutoIgnore = false)
    {
        var values = await deviceCacheManager.ReadDiscreteRegisterAsync(notAutoIgnore);
        var serialNumber = await GetSerialNumber(discreteRegisterDevice.ToString());
        var inversion = await GetInversion(discreteRegisterDevice.ToString());
        return values[serialNumber % values.Length] == !inversion;
    }

    /// <summary>
    /// 设置保持寄存器
    /// </summary>
    /// <param name="holdingRegisterDevice">设备</param>
    /// <param name="digitalValue">数字量</param>
    /// <returns></returns>
    private async Task SetHoldingRegisterAsync(HoldingRegisterDeviceEnum holdingRegisterDevice, decimal digitalValue)
    {
        string sensorName = holdingRegisterDevice.ToString();
        var serialNumber = await GetSerialNumber(sensorName);
        var analogValue = await ConvertAnalogValue(sensorName, digitalValue);
        await deviceCacheManager.WriteSingleRegisterAsync(new() { Value = analogValue, Address = serialNumber });
    }

    /// <summary>
    /// 传感器修正
    /// </summary>
    /// <param name="holdingRegisterDevice"></param>
    /// <param name="digitalValue"></param>
    /// <returns></returns>
    public async Task CorrectAsync(HoldingRegisterDeviceEnum holdingRegisterDevice, decimal digitalValue)
    {
        await SetHoldingRegisterAsync(holdingRegisterDevice, digitalValue);
    }

    /// <summary>
    /// 设置控制信号
    /// </summary>
    /// <returns></returns>
    public async Task SetControlSignalAsync(HoldingRegisterDeviceEnum device, decimal digitalValue)
    {
        await SetHoldingRegisterAsync(device, digitalValue);
    }

    /// <summary>
    /// 设置比例阀压力
    /// </summary>
    /// <returns></returns>
    public async Task SetPressureAsync(HoldingRegisterDeviceEnum device, decimal digitalValue)
    {
        await SetHoldingRegisterAsync(device, digitalValue);
    }

    /// <summary>
    /// 读取保持寄存器
    /// </summary>
    /// <param name="holdingRegisterDevice">设备</param>
    /// <param name="notAutoIgnore"></param>
    /// <returns></returns>
    public async Task<decimal> GetHoldingRegisterAsync(HoldingRegisterDeviceEnum holdingRegisterDevice, bool notAutoIgnore = false)
    {
        string sensorName = holdingRegisterDevice.ToString();
        var values = await deviceCacheManager.ReadHoldingRegisterAsync(notAutoIgnore);
        var serialNumber = await GetSerialNumber(sensorName);
        var analogValue = values[serialNumber % values.Length];
        var digitalValue = await ConvertDigitalValue(sensorName, analogValue);
        return digitalValue;
    }

    /// <summary>
    /// 获取偏移量
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetInputOffsetAsync(HoldingRegisterDeviceEnum device, bool notAutoIgnore = false)
    {
        return await GetHoldingRegisterAsync(device, notAutoIgnore);
    }

    /// <summary>
    /// 获取输入寄存器值
    /// </summary>
    /// <returns></returns>
    private async Task<decimal> GetInputRegisterAsync(InputRegisterDeviceEnum inputRegisterDevice, bool notAutoIgnore = false)
    {
        var values = await deviceCacheManager.ReadInputRegisterAsync(notAutoIgnore);
        var sensorName = inputRegisterDevice.ToString();
        var serialNumber = await GetSerialNumber(sensorName);
        var analogValue = values[serialNumber % values.Length];
        var digitalValue = await ConvertDigitalValue(sensorName, analogValue);
        return digitalValue;
    }

    ///// <summary>
    ///// 获取输入寄存器值
    ///// </summary>
    ///// <returns></returns>
    //private async Task<decimal> GetHighPrecisionInputRegisterAsync(InputRegisterDeviceEnum inputRegisterDevice, bool notAutoIgnore = false)
    //{
    //    var values = await deviceCacheManager.ReadInputRegisterAsync(notAutoIgnore);
    //    var sensorName = inputRegisterDevice.ToString();
    //    var serialNumber = await GetSerialNumber(sensorName);
    //    var analogValue1 = values[serialNumber % values.Length];
    //    var analogValue2 = values[(serialNumber + 1) % values.Length];
    //    var analogValue = (((uint)analogValue1) << 16) + analogValue2;
    //    var digitalValue = await ConvertDigitalValueHighPrecision(sensorName, analogValue);
    //    return digitalValue;
    //}


    /// <summary>
    /// 获取阀位反馈
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetValvePositionFeedbackAsync(InputRegisterDeviceEnum device, bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(device, notAutoIgnore);
    }

    /// <summary>
    /// 获取阀前压力
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetPressureInFrontOfValveAsync(bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(InputRegisterDeviceEnum.阀前压力, notAutoIgnore);
    }

    /// <summary>
    /// 获取压差
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetPressureDropAsync(bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(InputRegisterDeviceEnum.压差, notAutoIgnore);
    }

    /// <summary>
    /// 获取环境温度
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetTemperatureAsync(bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(InputRegisterDeviceEnum.环境温度, notAutoIgnore);
    }

    /// <summary>
    /// 获取流量计
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetFlowmeterAsync(bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(InputRegisterDeviceEnum.流量计, notAutoIgnore);
    }

    /// <summary>
    /// 获取液位计
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetLiquidLevelAsync(bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(InputRegisterDeviceEnum.液位计, notAutoIgnore);
    }

    /// <summary>
    /// 获取变频器转速
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetVariableFrequencyDriveSpeedAsync(bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(InputRegisterDeviceEnum.变频器转速, notAutoIgnore);
    }

    /// <summary>
    /// 获取变频器电流
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetInverterCurrentAsync(bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(InputRegisterDeviceEnum.变频器电流, notAutoIgnore);
    }

    /// <summary>
    /// 获取模拟量输入
    /// </summary>
    /// <returns></returns>
    public async Task<decimal> GetAnalogInputAsync(InputRegisterDeviceEnum device, bool notAutoIgnore = false)
    {
        return await GetInputRegisterAsync(device, notAutoIgnore);
    }

}
