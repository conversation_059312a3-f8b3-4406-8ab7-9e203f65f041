using System.Runtime.CompilerServices;
using WoowApp.Services.Shared.JsonSerialize;

namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 设备服务
/// </summary>
public class DeviceManager : IDeviceManager
{
    private readonly WsClientContainer wsClientContainer;
    private readonly ServiceContext serviceContext;
    private CurrentClientInfo currentClientInfo => serviceContext.CurrentClient;
    private readonly ClientContainer clientContainer;
    private readonly DeviceOperateTimeManager deviceOperateTimeManager;
    private readonly TransferManager<string> transferManager;

    public DeviceManager(
        WsClientContainer wsClientContainer,
        ServiceContext serviceContext,
        ClientContainer clientContainer,
        DeviceOperateTimeManager deviceOperateTimeManager,
        TransferManager<string> transferManager)
    {
        this.wsClientContainer = wsClientContainer;
        this.serviceContext = serviceContext;
        this.clientContainer = clientContainer;
        this.deviceOperateTimeManager = deviceOperateTimeManager;
        this.transferManager = transferManager;
    }

    /// <summary>
    /// 获取值(测试用，随机数)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task<List<decimal>> GetValues(DeviceManagerGetValuesInput input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<List<decimal>>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 设置值（测试用）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> SetValue(DeviceManagerSetValueInput<decimal> input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<bool>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 获取本地串口信息
    /// </summary>
    /// <returns></returns>
    public async Task<List<SerialPortModel>> GetSerialPortModels()
    {
        CheckCurrentInfo();
        return await ExecAsync<List<SerialPortModel>>(currentClientInfo.Key, "");
    }

    /// <summary>
    /// 设置串口信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> SetSerialPortInput(DeviceManagerSetSerialPortInput input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<bool>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 检查客户端信息
    /// </summary>
    private void CheckCurrentInfo()
    {
        if (string.IsNullOrEmpty(clientContainer.GetConnectionId(currentClientInfo.ClientId))) throw Oops.Oh("客户端未连接");
        if (string.IsNullOrEmpty(currentClientInfo.Key)) throw Oops.Oh("客户端关键信息不能为空");
    }

    /// <summary>
    /// 调用设备端执行(由于客户端使用AOT暂不支持对象类型通讯，只能使用字符串传输手写JSON转换代码)
    /// </summary>
    /// <param name="key"></param>
    /// <param name="inputJson"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    private async Task<TOutput> ExecAsync<TOutput>(string key, string inputJson, [CallerMemberName] string action = "")
    {
        var connectionId = clientContainer.GetConnectionId(currentClientInfo.ClientId);
        var client = wsClientContainer.GetClient(connectionId);
        var messageKey = Guid.NewGuid().ToString("N");
        await client.SendDataAsync(new WsClientInput { MsgType = "OnAction", Action = action, Key = messageKey, InputJson = inputJson });
        var transfer = transferManager.GetTransfer(messageKey);
        var outputJson = await transfer.GetReult();
        transferManager.RemoveTransfer(messageKey);
        deviceOperateTimeManager.Log(key);
        var output = outputJson.ToObject<DeviceManagerOutput<TOutput>>();
        if (!output.IsSuccess) throw Oops.Oh($"{nameof(DeviceManager)}执行{action}失败：{output.Message}");
        return output.Data;
    }

    /// <summary>
    /// 读取线圈
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool[]> ReadCoilsAsync(DeviceManagerGetValuesInput input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<bool[]>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 读取状态寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool[]> ReadInputsAsync(DeviceManagerGetValuesInput input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<bool[]>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 读取保持寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<ushort[]> ReadHoldingRegistersAsync(DeviceManagerGetValuesInput input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<ushort[]>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 读取输入寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<ushort[]> ReadInputRegistersAsync(DeviceManagerGetValuesInput input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<ushort[]>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 写入单个线圈寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> WriteSingleCoilAsync(DeviceManagerSetValueInput<bool> input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<bool>(currentClientInfo.Key, inputJson);
    }

    /// <summary>
    /// 写入单个保持寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> WriteSingleRegisterAsync(DeviceManagerSetValueInput<ushort> input)
    {
        CheckCurrentInfo();
        var inputJson = JSON.Serialize(input);
        return await ExecAsync<bool>(currentClientInfo.Key, inputJson);
    }

}
