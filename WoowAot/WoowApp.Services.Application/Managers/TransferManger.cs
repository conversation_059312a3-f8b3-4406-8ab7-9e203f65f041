using System.Collections.Concurrent;
namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 数据转换服务
/// </summary>
/// <typeparam name="T"></typeparam>
public class TransferManager<T>
{

    private ConcurrentDictionary<string, TransferItemManager<T>> DictTransfers = new();

    /// <summary>
    /// 获取数据转换项
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public TransferItemManager<T> GetTransfer(string key)
    {
        var transferItem = DictTransfers.GetOrAdd($"{key}", k => new TransferItemManager<T>());
        return transferItem;
    }

    /// <summary>
    /// 删除数据转换项
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public TransferItemManager<T> RemoveTransfer(string key)
    {
        DictTransfers.TryRemove(key, out var transferItem);
        return transferItem;
    }
}
