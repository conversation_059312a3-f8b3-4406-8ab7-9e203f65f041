using System.Collections.Concurrent;
using System.Threading;
using WoowApp.Services.Shared.Base;
namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 数据采集日志管理
/// </summary>
/// <typeparam name="T"></typeparam>
public class InspectLogMananger<T>
{
    /// <summary>
    /// 环形日志存储结构
    /// </summary>
    private class CircularLogBuffer
    {
        private readonly LogData<T>[] buffer;
        private int head;  // 指向下一个写入位置
        private int tail;  // 指向下一个读取位置
        private int count; // 当前存储的数据数量
        private readonly int capacity;
        private readonly object lockObject = new();
        private long startIndex; // 全局起始序号

        public CircularLogBuffer(int capacity = 1000)
        {
            this.capacity = capacity;
            buffer = new LogData<T>[capacity];
            startIndex = 0;
        }

        /// <summary>
        /// 添加日志数据
        /// </summary>
        /// <param name="data"></param>
        public void Add(LogData<T> data)
        {
            lock (lockObject)
            {
                buffer[head] = data;
                head = (head + 1) % capacity;

                if (count < capacity)
                {
                    count++;
                }
                else
                {
                    // 缓冲区已满，移动tail指针
                    tail = (tail + 1) % capacity;
                    startIndex++; // 起始序号递增
                }
            }
        }

        /// <summary>
        /// 获取指定全局索引的数据
        /// </summary>
        /// <param name="globalIndex">全局索引</param>
        /// <returns></returns>
        public LogData<T>? GetByGlobalIndex(long globalIndex)
        {
            lock (lockObject)
            {
                if (globalIndex < startIndex || globalIndex >= startIndex + count)
                    return null;
                var offset = (int)(globalIndex - startIndex);
                var actualIndex = (tail + offset) % capacity;
                return buffer[actualIndex];
            }
        }

        /// <summary>
        /// 获取当前存储的数据数量
        /// </summary>
        public int Count
        {
            get
            {
                lock (lockObject)
                {
                    return count;
                }
            }
        }

        /// <summary>
        /// 当前可读的起始全局索引
        /// </summary>
        public long StartIndex
        {
            get { lock (lockObject) { return startIndex; } }
        }

        /// <summary>
        /// 当前可读的最大全局索引（不含）
        /// </summary>
        public long EndIndex
        {
            get { lock (lockObject) { return startIndex + count; } }
        }

        /// <summary>
        /// 清空缓冲区
        /// </summary>
        public void Clear()
        {
            lock (lockObject)
            {
                head = 0;
                tail = 0;
                count = 0;
                Array.Clear(buffer, 0, buffer.Length);
            }
        }
    }

    /// <summary>
    /// 存日志 - 使用环形缓冲区
    /// </summary>
    private ConcurrentDictionary<string, CircularLogBuffer> DictLogs = new();

    /// <summary>
    /// 存令牌
    /// </summary>
    private ConcurrentDictionary<string, CancellationTokenSource> DictCts = new();

    /// <summary>
    /// 存任务
    /// </summary>
    private ConcurrentDictionary<string, Task> DictTask = new();

    public void SaveLog(LogData<T> data)
    {
        var logBuffer = DictLogs.GetOrAdd($"{data.Key}", k => new CircularLogBuffer(1000));
        logBuffer.Add(data);
    }

    public void Clear(string key)
    {
        DictLogs.TryRemove(key, out _);
        DictCts.TryRemove(key, out _);
    }

    public List<LogData<T>> GetHistoryLogs(string key)
    {
        var logBuffer = DictLogs.GetOrAdd($"{key}", k => new CircularLogBuffer(1000));
        var result = new List<LogData<T>>();
        var start = logBuffer.StartIndex;
        var end = logBuffer.EndIndex;
        for (long i = start; i < end; i++)
        {
            var data = logBuffer.GetByGlobalIndex(i);
            if (data != null)
            {
                result.Add(data);
            }
        }
        return result;
    }

    public bool IsEmpty(string key)
    {
        var logBuffer = DictLogs.GetOrAdd($"{key}", k => new CircularLogBuffer(1000));
        return logBuffer.Count == 0;
    }

    public CancellationTokenSource GetOrAddCts(string key)
    {
        var cts = DictCts.GetOrAdd($"{key}", k => new CancellationTokenSource());
        return cts;
    }

    public CancellationTokenSource? GetCts(string key)
    {
        if (DictCts.TryGetValue($"{key}", out CancellationTokenSource cts))
        {
            return cts;
        }
        else
        {
            return null;
        }
    }

    /// <summary>
    /// 重置令牌
    /// </summary>
    /// <param name="key"></param>
    public void RenewCts(string key)
    {
        DictCts.TryRemove(key, out _);
        DictCts.TryAdd(key, new CancellationTokenSource());
    }

    /// <summary>
    /// 存储任务
    /// </summary>
    /// <param name="key"></param>
    /// <param name="task"></param>
    /// <returns></returns>
    public Task SaveTask(string key, Task task)
    {
        if (DictTask.TryAdd(key, task))
        {

        }
        return task;
    }

    /// <summary>
    /// 获取任务
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public Task GetTask(string key)
    {
        if (DictTask.TryGetValue($"{key}", out Task task))
        {
            return task;
        }
        else
        {
            return null;
        }
    }

    /// <summary>
    /// 获取日志
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public async IAsyncEnumerable<LogData<T>> GetLogs(string key)
    {
        var logBuffer = DictLogs.GetOrAdd($"{key}", k => new CircularLogBuffer(1000));
        var currentIndex = logBuffer.StartIndex; // 全局递增索引
        var waitCount = 0;
        var maxWaitCount = 5000; // 约75秒没动作，自动判定为异常
        while (waitCount < maxWaitCount)
        {
            var endIndex = logBuffer.EndIndex;
            if (currentIndex < endIndex)
            {
                // 有新数据可读
                for (; currentIndex < endIndex; currentIndex++)
                {
                    var data = logBuffer.GetByGlobalIndex(currentIndex);
                    if (data != null)
                    {
                        yield return data;
                        if (data.Action is LogDataActionEnum.失败 or LogDataActionEnum.完成)
                        {
                            yield break;
                        }
                    }
                }
                waitCount = 0;
            }
            else
            {
                waitCount++;
                await Task.Delay(15);
            }
        }
        if (waitCount >= maxWaitCount)
        {
            yield return new LogData<T> { Action = LogDataActionEnum.失败, LogTime = DateTime.Now, Message = "等待超时" };
        }
    }
}
