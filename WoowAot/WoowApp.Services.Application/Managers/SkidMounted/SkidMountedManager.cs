using Microsoft.AspNetCore.DataProtection.KeyManagement;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.Diagnostics;
using Microsoft.Extensions.Caching.Memory;
using WoowApp.Services.Application.Models;

namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 撬装服务
/// </summary>
public class SkidMountedManager : IScoped
{
    private readonly PressureControlManager pressureControlManager;
    private readonly ControlDeviceManager controlDevice;
    private readonly InspectLogMananger<string> inspectLogMananger;
    private readonly ControlParams controlParams;
    private readonly ServiceContext serviceContext;
    private readonly ClientContainer container;
    private readonly ILogger<SkidMountedManager> logger;
    private DateTime? startTime;
    private readonly IMemoryCache memoryCache;
    
    /// <summary>
    /// 撬装服务
    /// </summary>
    /// <param name="pressureControlManager"></param>
    /// <param name="controlDeviceManager"></param>
    /// <param name="inspectLogMananger"></param>
    /// <param name="controlParams"></param>
    /// <param name="serviceContext"></param>
    /// <param name="container"></param>
    /// <param name="logger"></param>
    public SkidMountedManager(
        PressureControlManager pressureControlManager,
        ControlDeviceManager controlDeviceManager,
        InspectLogMananger<string> inspectLogMananger,
        ControlParams controlParams,
        ServiceContext serviceContext,
        ClientContainer container,
        ILogger<SkidMountedManager> logger, IMemoryCache memoryCache)
    {
        this.pressureControlManager = pressureControlManager;
        this.controlDevice = controlDeviceManager;
        this.inspectLogMananger = inspectLogMananger;
        this.controlParams = controlParams;
        this.serviceContext = serviceContext;
        this.container = container;
        this.logger = logger;
        this.memoryCache = memoryCache;
    }

    ///// <summary>
    ///// 任务令牌
    ///// </summary>
    //public static CancellationTokenSource Cts { get; set; } = new CancellationTokenSource();

    /// <summary>
    /// 撬装服务
    /// </summary>
    /// <returns></returns>
    public async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var key = AppConst.JobId;
        startTime = DateTime.Now;
        try
        {
            while (string.IsNullOrEmpty(container.GetConnectionId(AppConst.JobId))
                && !stoppingToken.IsCancellationRequested)
            {
                logger.LogInformation("等待客户端连接");
                await Task.Delay(1000, stoppingToken);
            }

            logger.LogInformation("撬装服务开始运行");
            inspectLogMananger.SaveLog(new LogData<string>() { Key = key, Action = LogDataActionEnum.开始 });

            await controlDevice.SetSolenoidValveStateAsync(CoilRegisterDeviceEnum.电动开关阀开信号, SolenoidValveStatusEnum.开启);
            await controlDevice.SetSolenoidValveStateAsync(CoilRegisterDeviceEnum.气动开关阀1电磁阀, SolenoidValveStatusEnum.开启);
            await controlDevice.SetSolenoidValveStateAsync(CoilRegisterDeviceEnum.气动开关阀2电磁阀, SolenoidValveStatusEnum.开启);
            await controlDevice.SetControlSignalAsync(HoldingRegisterDeviceEnum.气动调节阀1控制信号, 50);
            await controlDevice.SetControlSignalAsync(HoldingRegisterDeviceEnum.气动调节阀2控制信号, 50);

            while (!stoppingToken.IsCancellationRequested)
            {
                var pause = memoryCache.Get("pause") ?? false;
                if (pause is true)
                {
                    await Task.Delay(500, stoppingToken);
                    continue;
                }
                
                //logger.LogInformation("撬装运行中");
                try
                {
                    await CollectAsync();
                    await pressureControlManager.ControlPressure();
                    await Task.Delay(100, stoppingToken);
                }
                catch { }
            }
            inspectLogMananger.SaveLog(new LogData<string>() { Key = key, Action = LogDataActionEnum.完成 });
        }
        catch (Exception ex)
        {
            inspectLogMananger.SaveLog(new LogData<string>() { Key = key, Action = LogDataActionEnum.失败, Message = $"异常中止：{ex.Message}" });
            logger.LogError($"撬装服务异常：{ex.Message}。{ex.StackTrace}");
        }

        logger.LogInformation("撬装服务停止运行");
    }


    /// <summary>
    /// 用于减少重复检测
    /// </summary>
    private Stopwatch? checkStopButtonStopwatch;
    /// <summary>
    /// 操作设备前，需要检测急停状态
    /// </summary>
    /// <returns></returns>
    public async Task CheckStopButtonAsync()
    {
        if (checkStopButtonStopwatch == null || checkStopButtonStopwatch.Elapsed.TotalSeconds > 0.3) // 0.3秒内不重复检测
        {
            if (await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.急停开关)) throw Oops.Oh("用户已开启急停状态");
            checkStopButtonStopwatch = Stopwatch.StartNew();
        }
    }

    private async Task<SensorDataOutput> CollectAsync()
    {
        var now = DateTime.Now; //方便统一时间
        var key = AppConst.JobId;

        Stopwatch stopwatch = Stopwatch.StartNew();

        SensorDataOutput result = new()
        {
            Time = Round3((decimal)(now - startTime!.Value).TotalSeconds),
            PressureDropTarget = controlParams.Mode == ControlModeEnum.AutoStablePressureDrop ? (decimal)controlParams.SetPoint : null,
            ValvePrePressureTarget = controlParams.Mode == ControlModeEnum.AutoStabilizingPressure ? (decimal)controlParams.SetPoint : null,
        };

        result.ValvePrePressure = Round3(await controlDevice.GetPressureInFrontOfValveAsync(true));
        result.PressureDrop = Round3(await controlDevice.GetPressureDropAsync());
        result.Temperature = Round3(await controlDevice.GetTemperatureAsync());
        result.LiquidLevel = Round3(await controlDevice.GetLiquidLevelAsync());
        result.Flowmeter = Round3(await controlDevice.GetFlowmeterAsync());
        result.VariableFrequencyDriveSpeed = Round3(await controlDevice.GetVariableFrequencyDriveSpeedAsync());
        result.InverterCurrent = Round3(await controlDevice.GetInverterCurrentAsync());
        result.ElectricSwitchValvePositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.电动开关阀阀位反馈));
        result.Valve1PositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.气动调节阀1阀位反馈));
        result.Valve2PositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.气动调节阀2阀位反馈));
        result.Valve3PositionFeedback = Round3(await controlDevice.GetValvePositionFeedbackAsync(InputRegisterDeviceEnum.气动调节阀3阀位反馈));

        result.InverterFault = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器故障);
        result.InverterOperation = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器运行);
        result.InverterReady = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器就绪);
        result.InverterAlarm = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.变频器报警);

        result.ElectricSwitchValveOpenPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.电动开关阀开位);
        result.ElectricSwitchValveClosedPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.电动开关阀关位);
        result.PneumaticSwitchValve1OpenPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀1开位);
        result.PneumaticSwitchValve1ClosedPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀1关位);
        result.PneumaticSwitchValve2OpenPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀2开位);
        result.PneumaticSwitchValve2ClosedPosition = await controlDevice.GetFeedbackStateAsync(DiscreteRegisterDeviceEnum.气动开关阀2关位);

        result.CollectTime = Round3((decimal)stopwatch.Elapsed.TotalMilliseconds);
        stopwatch.Restart();
        await CheckStopButtonAsync();
        result.CheckTime = Round3((decimal)stopwatch.Elapsed.TotalMilliseconds);

        serviceContext.CurrentClient.SensorData = result;
        var dataJson = result.ToJson();
        inspectLogMananger.SaveLog(new LogData<string>() { Key = key, Action = LogDataActionEnum.数据日志, Data = dataJson });

        logger.LogDebug($"采集数据：{dataJson}");
        return result;
    }

    /// <summary>
    /// 保留3位小数
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    private decimal Round3(decimal value) => decimal.Round(value, 3);

}
