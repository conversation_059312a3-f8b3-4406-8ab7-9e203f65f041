using WoowApp.Services.Shared.JsonSerialize;

namespace WoowApp.Services.Application.Managers.Dto;

/// <summary>
/// 设备数据
/// </summary>
[JsonSerializeRegister]
public class SensorDataOutput
{
    /// <summary>
    /// 时间
    /// </summary>
    public decimal Time { get; set; }

    /// <summary>
    /// 采集时间
    /// </summary>
    public decimal? CollectTime { get; set; }

    /// <summary>
    /// 检查急停按钮时间
    /// </summary>
    public decimal? CheckTime { get; set; }

    /// <summary>
    /// 阀前压力目标值
    /// </summary>
    public decimal? ValvePrePressureTarget { get; set; }

    /// <summary>
    /// 阀前压差目标值
    /// </summary>
    public decimal? PressureDropTarget { get; set; }

    /// <summary>
    /// 阀前压力
    /// </summary>
    public decimal? ValvePrePressure { get; set; }

    /// <summary>
    /// 阀前压差
    /// </summary>
    public decimal? PressureDrop { get; set; }

    /// <summary>
    /// 温度
    /// </summary>
    public decimal? Temperature { get; set; }

    /// <summary>
    /// 液位计
    /// </summary>
    public decimal? LiquidLevel { get; set; }

    /// <summary>
    /// 流量计
    /// </summary>
    public decimal? Flowmeter { get; set; }

    /// <summary>
    /// 变频器转速
    /// </summary>
    public decimal? VariableFrequencyDriveSpeed { get; set; }

    /// <summary>
    /// 变频器电流
    /// </summary>
    public decimal? InverterCurrent { get; set; }

    ///// <summary>
    ///// 湿度
    ///// </summary>
    //public decimal? Humidity { get; set; }

    /// <summary>
    /// 电动开关阀阀位反馈
    /// </summary>
    public decimal? ElectricSwitchValvePositionFeedback { get; set; }

    /// <summary>
    /// 气动调节阀1阀位反馈
    /// </summary>
    public decimal? Valve1PositionFeedback { get; set; }

    /// <summary>
    /// 气动调节阀2阀位反馈
    /// </summary>
    public decimal? Valve2PositionFeedback { get; set; }

    /// <summary>
    /// 气动调节阀3阀位反馈
    /// </summary>
    public decimal? Valve3PositionFeedback { get; set; }

    /// <summary>
    /// 变频器故障
    /// </summary>
    public bool? InverterFault { get; set; }

    /// <summary>
    /// 变频器运行
    /// </summary>
    public bool? InverterOperation { get; set; }

    /// <summary>
    /// 变频器就绪
    /// </summary>
    public bool? InverterReady { get; set; }

    /// <summary>
    /// 变频器报警
    /// </summary>
    public bool? InverterAlarm { get; set; }

    /// <summary>
    /// 电动开关阀开位
    /// </summary>
    public bool? ElectricSwitchValveOpenPosition { get; set; }

    /// <summary>
    /// 电动开关阀关位
    /// </summary>
    public bool? ElectricSwitchValveClosedPosition { get; set; }

    /// <summary>
    /// 气动开关阀1开位
    /// </summary>
    public bool? PneumaticSwitchValve1OpenPosition { get; set; }

    /// <summary>
    /// 气动开关阀1关位
    /// </summary>
    public bool? PneumaticSwitchValve1ClosedPosition { get; set; }

    /// <summary>
    /// 气动开关阀2开位
    /// </summary>
    public bool? PneumaticSwitchValve2OpenPosition { get; set; }

    /// <summary>
    /// 气动开关阀2关位
    /// </summary>
    public bool? PneumaticSwitchValve2ClosedPosition { get; set; }

}
