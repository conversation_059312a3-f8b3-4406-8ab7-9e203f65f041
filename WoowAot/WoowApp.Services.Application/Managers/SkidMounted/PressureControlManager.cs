using Microsoft.Extensions.Logging;
using WoowApp.Services.Application.SeedData;

namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 压力控制管理
/// </summary>
public class PressureControlManager : IScoped
{
    private readonly PidParams _pid = new();
    private readonly ILogger<PressureControlManager> logger;
    private readonly ServiceContext serviceContext;
    private readonly List<ValveOperatorStationSensor> SeedData;
    /// <summary>
    /// 控制参数
    /// </summary>
    private readonly ControlParams controlParams;
    private readonly ControlDeviceManager controlDevice;
    private bool _pidLockReset = true;
    private uint _ctrlCount;

    /// <summary>
    /// 压力控制
    /// </summary>
    public PressureControlManager(
        ILogger<PressureControlManager> logger,
        ServiceContext serviceContext,
        ControlDeviceManager controlDeviceManager,
        ControlParams controlParams
        )
    {
        this.logger = logger;
        this.serviceContext = serviceContext;
        this.controlDevice = controlDeviceManager;
        this.controlParams = controlParams;

        SeedData = [.. new ValveOperatorStationSensorSeedData().HasData()];
        controlParams.OnChangeMode = (_, _) => { Reset(); };
    }

    /// <summary>
    /// 控制压力
    /// </summary>
    /// <returns></returns>
    public async Task<int> ControlPressure()
    {
        if (++_ctrlCount < controlParams.Duty)
            return 0;

        _ctrlCount = 0;

        if (controlParams.Mode == ControlModeEnum.Off)
            return await HandleOffMode();

        if (!await GetSetValues())
        {
            logger.LogWarning("设备未就绪，无法自动控制！");
            return -3;
        }

        return controlParams.Mode switch
        {
            ControlModeEnum.Manual => await HandleManualMode(),
            ControlModeEnum.AutoStabilizingPressure => await HandleAutoMode(),
            ControlModeEnum.AutoStablePressureDrop => await HandleAutoMode(),
            _ => -4
        };
    }

    /// <summary>
    /// 获取设置值
    /// </summary>
    /// <returns></returns>
    private async Task<bool> GetSetValues()
    {

        var sensorData = serviceContext.CurrentClient.SensorData;
        if (
            sensorData.ElectricSwitchValvePositionFeedback < 20 ||
            //sensorData.ElectricSwitchValveOpenPosition == false ||
            sensorData.PneumaticSwitchValve1OpenPosition == false ||
            sensorData.PneumaticSwitchValve2OpenPosition == false
            ) // 相关阀门未开到位
        {
            return false;
        }

        //if (sensorData.ValvePrePressure >= 500) // 设定压力目标值  PT01<5bar
        //{
        //    return false;
        //}

        //if (sensorData.VariableFrequencyDriveSpeed <= 100) // 下来需按实测修改 to do
        //{
        //    return false;
        //}
        return await Task.FromResult(true);
    }

    /// <summary>
    /// 关闭模式
    /// </summary>
    /// <returns></returns>
    private async Task<int> HandleOffMode()
    {

        await controlDevice.SetControlSignalAsync(HoldingRegisterDeviceEnum.变频器调节信号, 0);

        var c = 0;
        while (await controlDevice.GetVariableFrequencyDriveSpeedAsync() > 10 && c < 100)
        {
            await Task.Delay(100);
            c++;
        }
        if (await controlDevice.GetVariableFrequencyDriveSpeedAsync() > 10)
        {
            logger.LogInformation("变频器未能正常关闭!");
            return 0;
        }

        await controlDevice.SetSolenoidValveStateAsync(CoilRegisterDeviceEnum.电动开关阀开信号, SolenoidValveStatusEnum.关闭);
        await controlDevice.SetSolenoidValveStateAsync(CoilRegisterDeviceEnum.气动开关阀1电磁阀, SolenoidValveStatusEnum.关闭);
        await controlDevice.SetSolenoidValveStateAsync(CoilRegisterDeviceEnum.气动开关阀2电磁阀, SolenoidValveStatusEnum.关闭);
        await controlDevice.SetControlSignalAsync(HoldingRegisterDeviceEnum.气动调节阀1控制信号, 0);
        await controlDevice.SetControlSignalAsync(HoldingRegisterDeviceEnum.气动调节阀2控制信号, 0);

        Reset();
        logger.LogInformation("System off!");
        return await Task.FromResult(1);
    }

    /// <summary>
    /// 手动模式
    /// </summary>
    /// <returns></returns>
    private async Task<int> HandleManualMode()
    {
        logger.LogInformation("System manual mode!");
        Reset();
        //await OutputToActuators();
        return await Task.FromResult(1);
    }

    /// <summary>
    /// 自动模式
    /// </summary>
    /// <returns></returns>
    private async Task<int> HandleAutoMode()
    {
        logger.LogInformation("System auto mode!");

        if (ResetPid())
        {
            logger.LogInformation("PID clear success!");
        }

        UpdatePidParameters();

        if (!await ComputePid())
            return -1;

        if (controlParams.Mode == ControlModeEnum.AutoStabilizingPressure)
        {// 稳压力
            ValveOperatorStationSensor sensor = SeedData.Where(u => u.SensorName == HoldingRegisterDeviceEnum.变频器调节信号.ToString()).First();
            // Scale PID output to control range
            var output = ScaleValue(
                _pid.Output,
                _pid.MinOutput, _pid.MaxOutput,
                (float)sensor.MeasureMin!, (float)sensor.MeasureMax!
            );
            await controlDevice.SetControlSignalAsync(HoldingRegisterDeviceEnum.变频器调节信号, (decimal)output);
            await controlDevice.SetSolenoidValveStateAsync(CoilRegisterDeviceEnum.变频器启动信号, SolenoidValveStatusEnum.开启);
        }
        else
        { // 稳压差
            ValveOperatorStationSensor sensor = SeedData.Where(u => u.SensorName == HoldingRegisterDeviceEnum.气动调节阀2控制信号.ToString()).First();
            // Scale PID output to control range
            var output = ScaleValue(
                _pid.Output,
                _pid.MinOutput, _pid.MaxOutput,
                (float)sensor.MeasureMin!, (float)sensor.MeasureMax!
            );

            await controlDevice.SetControlSignalAsync(HoldingRegisterDeviceEnum.气动调节阀2控制信号, (decimal)output);
        }
        return 1;
    }

    /// <summary>
    /// 重置Pid
    /// </summary>
    /// <returns></returns>
    public void Reset()
    {
        _pidLockReset = true;
    }

    /// <summary>
    /// 重置Pid
    /// </summary>
    /// <returns></returns>
    private bool ResetPid()
    {
        if (!_pidLockReset) return false;

        _pid.Integral = 0.0f;
        _pid.LastError = 0.0f;
        _pid.Output = 0.0f;

        _pidLockReset = false;
        return true;
    }

    /// <summary>
    /// 更新Pid参数
    /// </summary>
    /// <returns></returns>
    private void UpdatePidParameters()
    {
        _pid.Duty = controlParams.Duty;
        _pid.Kp = controlParams.Kp;
        _pid.Ki = controlParams.Ki;
        _pid.Kd = controlParams.Kd;
    }

    /// <summary>
    /// 计算Pid
    /// </summary>
    /// <returns></returns>
    private async Task<bool> ComputePid()
    {
        ValveOperatorStationSensor sensor = controlParams.Mode == ControlModeEnum.AutoStabilizingPressure
            ? SeedData.Where(u => u.SensorName == "阀前压力").First()
            : SeedData.Where(u => u.SensorName == "压差").First();

        _pid.SetPoint = ScaleValue(controlParams.SetPoint, (float)sensor.MeasureMin!.Value, (float)sensor.MeasureMax!.Value, _pid.MinOutput, _pid.MaxOutput);

        _pid.Input = ScaleValue((float)(controlParams.Mode == ControlModeEnum.AutoStabilizingPressure ?
            serviceContext.CurrentClient.SensorData.ValvePrePressure! :
            serviceContext.CurrentClient.SensorData.PressureDrop!),
            (float)sensor.MeasureMin!.Value, (float)sensor.MeasureMax!.Value, _pid.MinOutput, _pid.MaxOutput);

        float error = _pid.SetPoint - _pid.Input;
        _pid.Integral += error;

        // Anti-windup clamping
        float maxIntegral = _pid.MaxOutput * 0.7f;
        float minIntegral = -1 * _pid.MaxOutput * 0.7f;
        _pid.Integral = Math.Clamp(_pid.Integral, minIntegral, maxIntegral);

        float derivative = error - _pid.LastError;
        _pid.Output = _pid.Kp * error +
                      _pid.Ki * _pid.Integral +
                      _pid.Kd * derivative;

        // Clamp final output
        _pid.Output = Math.Clamp(_pid.Output, _pid.MinOutput, _pid.MaxOutput);
        logger.LogInformation($"Output={_pid.Output}, pErr={_pid.Kp * error:0.###}, iErr={_pid.Ki * _pid.Integral:0.###}, dErr={_pid.Kd * derivative:0.###}");

        _pid.LastError = error;
        return await Task.FromResult(true);
    }

    /// <summary>
    /// 线性转换函数
    /// </summary>
    /// <param name="value"></param>
    /// <param name="inMin"></param>
    /// <param name="inMax"></param>
    /// <param name="outMin"></param>
    /// <param name="outMax"></param>
    /// <returns></returns>
    private static float ScaleValue(float value, float inMin, float inMax, float outMin, float outMax)
    {
        return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
    }
}
