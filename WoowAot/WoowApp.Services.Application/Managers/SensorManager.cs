using WoowApp.Services.Application.SeedData;

namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 传感器辅助管理
/// </summary>
public class SensorManager : IScoped
{
    //private readonly ISysCacheService sysCacheService;
    //private readonly ServiceContext serviceContext;
    //private readonly SqlSugarRepository<ValveOperatorStation> operatorStationRepo;
    //private readonly SqlSugarRepository<ValveOperatorStationSensor> sensorRepo;

    public SensorManager(
       //ISysCacheService sysCacheService,
       //ServiceContext serviceContext,
       //SqlSugarRepository<ValveOperatorStation> operatorStationRepo,
       //SqlSugarRepository<ValveOperatorStationSensor> sensorRepo
        )
    {
        //this.sysCacheService = sysCacheService;
        //this.serviceContext = serviceContext;
        //this.operatorStationRepo = operatorStationRepo;
        //this.sensorRepo = sensorRepo;
        sensorList = [.. new ValveOperatorStationSensorSeedData().HasData()];
    }

    private List<ValveOperatorStationSensor> sensorList;
    /// <summary>
    /// 添加缓存实现
    /// </summary>
    /// <param name="sensorName"></param>
    /// <returns></returns>
    private async Task<ValveOperatorStationSensor> GetSensor(string sensorName)
    {
        var sensor = sensorList.Where(u => u.SensorName == sensorName).Select(u => u).FirstOrDefault() ?? throw Oops.Oh($"未找到设备{sensorName}配置。");
        return sensor;
    }

    /// <summary>
    /// 获取设备序号
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <returns></returns>
    public async Task<ushort> GetSerialNumber(string sensorName)
    {
        var sensor = await GetSensor(sensorName);
        if (sensor.SerialNumber == 0) throw Oops.Oh($"设备{sensorName}序号不能为零");
        return (ushort)(sensor.SerialNumber - 1);
    }

    /// <summary>
    /// 获取设备序号
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <returns></returns>
    public async Task<bool> GetInversion(string sensorName)
    {
        var sensor = await GetSensor(sensorName);
        return sensor?.Inversion ?? false;
    }

    /// <summary>
    /// 数字量转模拟量值
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <param name="digitalValue">数字量值</param>
    /// <returns>模拟量值</returns>
    public async Task<ushort> ConvertAnalogValue(string sensorName, decimal digitalValue)
    {
        var sensor = await GetSensor(sensorName);

        if (sensor.SensorType == SensorTypeEnum.数字量传感器) return ConvertToUshort(sensorName, digitalValue);

        if (!sensor.MeasureMin.HasValue || !sensor.MeasureMax.HasValue || sensor.MeasureMin >= sensor.MeasureMax) throw Oops.Oh("传感器数字量参数错误");
        if (!sensor.AnalogMin.HasValue || !sensor.AnalogMax.HasValue || sensor.AnalogMin >= sensor.AnalogMax) throw Oops.Oh("传感器模拟量参数错误");

        return ConvertToUshort(sensorName, SensorAnalogConvert.ToAnalogValue(digitalValue, sensor.MeasureMin.Value, sensor.MeasureMax.Value, sensor.AnalogMin.Value, sensor.AnalogMax.Value));
    }

    /// <summary>
    /// 支持部分负数转ushort
    /// </summary>
    /// <param name="sensorName"></param>
    /// <param name="digitalValue"></param>
    /// <returns></returns>
    private ushort ConvertToUshort(string sensorName, decimal digitalValue)
    {
        var value = sensorName.Contains("偏移量") || sensorName.Contains("补偿") ? unchecked((ushort)((short)digitalValue)) : digitalValue;
        return (ushort)value;
    }

    /// <summary>
    /// 模拟量转数字量值（模拟量输入2个字节）
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <param name="analogValue">模拟量值</param>
    /// <returns>数字量值</returns>
    public async Task<decimal> ConvertDigitalValue(string sensorName, ushort analogValue)
    {
        var sensor = await GetSensor(sensorName);
        if (sensor.SensorType == SensorTypeEnum.数字量传感器) return analogValue;

        if (!sensor.MeasureMin.HasValue || !sensor.MeasureMax.HasValue || sensor.MeasureMin >= sensor.MeasureMax) throw Oops.Oh("传感器数字量参数错误");
        if (!sensor.AnalogMin.HasValue || !sensor.AnalogMax.HasValue || sensor.AnalogMin >= sensor.AnalogMax) throw Oops.Oh("传感器模拟量参数错误");
        if (sensor.SensorConvertType == SensorConvertTypeEnum.Ntc100K_3950)
        {
            return Ntc3950.GetTemperature(analogValue);
        }

        decimal analogValueDec = sensor.SensorName.Contains("偏移量") || sensor.SensorName.Contains("补偿") ? unchecked((short)analogValue) : analogValue;
        return SensorAnalogConvert.ToDigitalValue(analogValueDec, sensor.MeasureMin.Value, sensor.MeasureMax.Value, sensor.AnalogMin.Value, sensor.AnalogMax.Value);
    }

    /// <summary>
    /// 高精度模拟量转数字量值(高精度模拟量输入4个字节)
    /// </summary>
    /// <param name="sensorName">传感器名称</param>
    /// <param name="analogValue">模拟量值</param>
    /// <returns>数字量值</returns>
    public async Task<decimal> ConvertDigitalValueHighPrecision(string sensorName, uint analogValue)
    {
        var sensor = await GetSensor(sensorName);
        if (sensor.SensorType == SensorTypeEnum.数字量传感器) return analogValue;

        if (!sensor.MeasureMin.HasValue || !sensor.MeasureMax.HasValue || sensor.MeasureMin >= sensor.MeasureMax) throw Oops.Oh("传感器数字量参数错误");
        if (!sensor.AnalogMin.HasValue || !sensor.AnalogMax.HasValue || sensor.AnalogMin >= sensor.AnalogMax) throw Oops.Oh("传感器模拟量参数错误");
        if (sensor.SensorConvertType == SensorConvertTypeEnum.Ntc100K_3950)
        {
            return Ntc3950.GetTemperature((ushort)analogValue);
        }
        decimal analogValueDec = sensor.SensorName.Contains("偏移量") || sensor.SensorName.Contains("补偿") ? unchecked((short)analogValue) : analogValue;
        return SensorAnalogConvert.ToDigitalValue(analogValueDec, sensor.MeasureMin.Value, sensor.MeasureMax.Value, sensor.AnalogMin.Value, sensor.AnalogMax.Value);
    }

}
