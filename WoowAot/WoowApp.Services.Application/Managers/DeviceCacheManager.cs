using System.Diagnostics;

namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 设备带缓存控制管理
/// </summary>
public class DeviceCacheManager : IScoped
{
    private readonly IDeviceManager deviceManager;

    public DeviceCacheManager(IDeviceManager deviceManager)
    {
        this.deviceManager = deviceManager;
    }

    /// <summary>
    /// 线圈寄存器开始地址(偏移量)
    /// </summary>
    private ushort coilRegisterStartAddress = 0x10;

    /// <summary>
    /// 从机地址
    /// </summary>
    private byte slaveAddress = 254;

    /// <summary>
    /// 最大缓存时间
    /// </summary>
    private TimeSpan maxCacheTime = TimeSpan.FromMilliseconds(5);

    /// <summary>
    /// 线圈寄存器缓存
    /// </summary>
    private bool[]? coilRegValues { get; set; }

    /// <summary>
    /// 最近读取线圈寄存器时间
    /// </summary>
    private Stopwatch lastReadCoilRegTime = new();

    /// <summary>
    /// 读取线圈寄存器到缓存
    /// </summary>
    /// <param name="notAutoIgnore">智能跳过从设备取值</param>
    /// <returns></returns>
    public async Task<bool[]> ReadCoilRegisterAsync(bool notAutoIgnore = false)
    {
        if (notAutoIgnore || coilRegValues == null || lastReadCoilRegTime.Elapsed > maxCacheTime)
        {
            coilRegValues = await deviceManager.ReadCoilsAsync(new() { SlaveId = slaveAddress, StartAddress = coilRegisterStartAddress, NumberOfPoints = 8 });
            lastReadCoilRegTime.Restart();
        }
        return coilRegValues;
    }

    /// <summary>
    /// 状态寄存器缓存
    /// </summary>
    private bool[]? discreteRegValues { get; set; }

    /// <summary>
    /// 最近读取状态寄存器时间
    /// </summary>
    private Stopwatch lastReadDiscreteRegTime = new();

    /// <summary>
    /// 读取状态寄存器到缓存
    /// </summary>
    /// <param name="notAutoIgnore">智能跳过从设备取值</param>
    /// <returns></returns>
    public async Task<bool[]> ReadDiscreteRegisterAsync(bool notAutoIgnore = false)
    {
        if (notAutoIgnore || discreteRegValues == null || lastReadDiscreteRegTime.Elapsed > maxCacheTime)
        {
            discreteRegValues = await deviceManager.ReadInputsAsync(new() { SlaveId = slaveAddress, StartAddress = 0x00, NumberOfPoints = 16 });
            lastReadDiscreteRegTime.Restart();
        }
        return discreteRegValues;
    }

    /// <summary>
    /// 保持寄存器缓存
    /// </summary>
    private ushort[]? holdingRegValues { get; set; }

    /// <summary>
    /// 最近读取保持寄存器时间
    /// </summary>
    private Stopwatch lastReadHoldingRegTime = new();

    /// <summary>
    /// 读取保持寄存器到缓存
    /// </summary>
    /// <param name="notAutoIgnore">智能跳过从设备取值</param>
    /// <returns></returns>
    public async Task<ushort[]> ReadHoldingRegisterAsync(bool notAutoIgnore = false)
    {
        if (notAutoIgnore || holdingRegValues == null || lastReadHoldingRegTime.Elapsed > maxCacheTime)
        {
            holdingRegValues = await deviceManager.ReadHoldingRegistersAsync(new() { SlaveId = slaveAddress, StartAddress = 0x00, NumberOfPoints = 4 });
            lastReadHoldingRegTime.Restart();
        }
        return holdingRegValues;
    }


    /// <summary>
    /// 输入寄存器开始地址(偏移量)
    /// </summary>
    private ushort inputRegisterStartAddress = 0x00;
    /// <summary>
    /// 输入寄存器缓存
    /// </summary>
    private ushort[]? inputRegValues { get; set; }

    /// <summary>
    /// 最近读取保持寄存器时间
    /// </summary>
    private Stopwatch lastReadInputRegTime = new();

    /// <summary>
    /// 读取输入寄存器到缓存
    /// </summary>
    /// <param name="notAutoIgnore">智能跳过从设备取值</param>
    /// <returns></returns>
    public async Task<ushort[]> ReadInputRegisterAsync(bool notAutoIgnore = false)
    {
        if (notAutoIgnore || inputRegValues == null || lastReadInputRegTime.Elapsed > maxCacheTime)
        {
            inputRegValues = await deviceManager.ReadInputRegistersAsync(new() { SlaveId = slaveAddress, StartAddress = inputRegisterStartAddress, NumberOfPoints = 16 });
            lastReadInputRegTime.Restart();
        }
        return inputRegValues;
    }

    /// <summary>
    /// 写入单个线圈寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task WriteSingleCoilAsync(DeviceManagerSetValueInput<bool> input)
    {
        coilRegValues = null;
        if (input.SlaveId == 0) input.SlaveId = slaveAddress;
        await deviceManager.WriteSingleCoilAsync(new() { SlaveId = input.SlaveId, Value = input.Value, Address = (ushort)(input.Address + coilRegisterStartAddress) });
    }

    /// <summary>
    /// 写入保持寄存器
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task WriteSingleRegisterAsync(DeviceManagerSetValueInput<ushort> input)
    {
        holdingRegValues = null;
        if (input.SlaveId == 0) input.SlaveId = slaveAddress;
        await deviceManager.WriteSingleRegisterAsync(new() { SlaveId = input.SlaveId, Value = input.Value, Address = (ushort)(input.Address + inputRegisterStartAddress) });
    }

}
