namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 数据转换服务项
/// </summary>
/// <typeparam name="T"></typeparam>
public class TransferItemManager<T>
{

    public TaskCompletionSource<T> Tcs = new TaskCompletionSource<T>();
    public async Task<T> GetReult()
    {
        Tcs = new();
        var result = await Tcs.Task.WaitAsync(TimeSpan.FromSeconds(2));
        return result;
    }
}
