namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 设备服务
/// </summary>
public interface IDeviceManager
{

    /// <summary>
    /// 读取线圈
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool[]> ReadCoilsAsync(DeviceManagerGetValuesInput input);

    /// <summary>
    /// 读取状态寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool[]> ReadInputsAsync(DeviceManagerGetValuesInput input);

    /// <summary>
    /// 读取保持寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ushort[]> ReadHoldingRegistersAsync(DeviceManagerGetValuesInput input);

    /// <summary>
    /// 读取输入寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ushort[]> ReadInputRegistersAsync(DeviceManagerGetValuesInput input);

    /// <summary>
    /// 写入单个线圈寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> WriteSingleCoilAsync(DeviceManagerSetValueInput<bool> input);

    /// <summary>
    /// 写入单个保持寄存器值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> WriteSingleRegisterAsync(DeviceManagerSetValueInput<ushort> input);

}
