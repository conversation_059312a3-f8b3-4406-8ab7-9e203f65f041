using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application.Managers;

/// <summary>
/// 设备通讯时间记录
/// </summary>
public class DeviceOperateTimeManager : ISingleton
{


    private ConcurrentDictionary<string, DateTime> LastOperateTimeDict = new();

    /// <summary>
    /// 记录上一次通讯时间
    /// </summary>
    /// <param name="holdId"></param>
    public void Log(string holdId)
    {
        LastOperateTimeDict[holdId] = DateTime.Now;
    }

    /// <summary>
    /// 获取上一次操作时间
    /// </summary>
    /// <param name="holdId"></param>
    /// <returns></returns>
    public DateTime? GetLastOperateTime(string holdId)
    {
        if (LastOperateTimeDict.ContainsKey(holdId))
        {
            return LastOperateTimeDict[holdId];
        }
        return null;
    }
}
