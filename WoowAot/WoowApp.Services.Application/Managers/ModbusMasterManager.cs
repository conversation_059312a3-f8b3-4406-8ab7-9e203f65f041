//using Microsoft.Extensions.Logging;
//using NModbus;
//using System;
//using System.Collections.Generic;
//using System.IO.Ports;
//using System.Linq;
//using System.Net.Sockets;
//using System.Text;
//using System.Threading.Tasks;

//namespace WoowApp.Services.Application.Managers;

///// <summary>
///// Modbus主机服务
///// </summary>
//public class ModbusMasterManager : IScoped
//{
//    private readonly ServiceContext serviceContext;
//    private readonly ILogger<ModbusMasterManager> logger;

//    public ModbusMasterManager(
//        ServiceContext serviceContext,
//        ILogger<ModbusMasterManager> logger)
//    {
//        this.serviceContext = serviceContext;
//        this.logger = logger;
//    }
    
//    private IModbusMaster? master;
//    /// <summary>
//    /// 获取Modbus主机
//    /// </summary>
//    /// <returns></returns>
//    /// <exception cref="Exception"></exception>
//    public IModbusMaster GetMaster()
//    {
//        if (master == null)
//        {
//            if (!string.IsNullOrWhiteSpace(serviceContext.CurrentClient.IpAddress) && serviceContext.CurrentClient.Port.HasValue)
//            {
//                var tcpClient = new TcpClient(serviceContext.CurrentClient.IpAddress, serviceContext.CurrentClient.Port.Value);
//                var factory = new ModbusFactory();
//                master = factory.CreateMaster(tcpClient);
//            }
//            else
//            {
//                throw new Exception("未配置客户端连接！");
//            }
//        }
//        return master;
//    }

//    /// <summary>
//    /// 重建主机服务
//    /// </summary>
//    /// <returns></returns>
//    public IModbusMaster ReConnect()
//    {
//        try
//        {
//            if (master != null)
//            {
//                master.Dispose();
//                master = null;
//            }
//        }
//        catch (Exception ex)
//        {
//            logger.LogError($"ReConnect关闭异常{ex.Message}");
//        }
//        return GetMaster();
//    }
//}
