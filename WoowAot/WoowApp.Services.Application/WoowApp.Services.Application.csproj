<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<DocumentationFile></DocumentationFile>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Riok.Mapperly" Version="3.6.0" ExcludeAssets="runtime" PrivateAssets="all" />
		<PackageReference Include="WoowApp.Services.Core" Version="1.0.37" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
		<PackageReference Include="System.Private.Uri" Version="4.3.2" />
	</ItemGroup>
</Project>
