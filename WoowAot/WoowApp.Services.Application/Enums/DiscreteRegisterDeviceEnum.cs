using System.ComponentModel;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 状态寄存器设备
/// </summary>
[ConstSelector("状态寄存器设备")]
public enum DiscreteRegisterDeviceEnum
{
    /// <summary>
    /// 变频器故障
    /// </summary>
    [Description("变频器故障")]
    变频器故障 = 0x020101,

    /// <summary>
    /// 变频器运行
    /// </summary>
    [Description("变频器运行")]
    变频器运行 = 0x020102,

    /// <summary>
    /// 变频器就绪
    /// </summary>
    [Description("变频器就绪")]
    变频器就绪 = 0x020103,

    /// <summary>
    /// 变频器报警
    /// </summary>
    [Description("变频器报警")]
    变频器报警 = 0x020104,

    /// <summary>
    /// 电动开关阀开位
    /// </summary>
    [Description("电动开关阀开位")]
    电动开关阀开位 = 0x020201,

    /// <summary>
    /// 电动开关阀关位
    /// </summary>
    [Description("电动开关阀关位")]
    电动开关阀关位 = 0x020202,

    /// <summary>
    /// 气动开关阀1开位
    /// </summary>
    [Description("气动开关阀1开位")]
    气动开关阀1开位 = 0x020203,

    /// <summary>
    /// 气动开关阀1关位
    /// </summary>
    [Description("气动开关阀1关位")]
    气动开关阀1关位 = 0x020204,

    /// <summary>
    /// 气动开关阀2开位
    /// </summary>
    [Description("气动开关阀2开位")]
    气动开关阀2开位 = 0x020205,

    /// <summary>
    /// 气动开关阀2关位
    /// </summary>
    [Description("气动开关阀2关位")]
    气动开关阀2关位 = 0x020206,

    /// <summary>
    /// 急停开关
    /// </summary>
    [Description("急停开关")]
    急停开关 = 0x020405,

}
