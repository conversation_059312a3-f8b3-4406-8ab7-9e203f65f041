using System.ComponentModel;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 线圈寄存器设备
/// </summary>
[ConstSelector("线圈寄存器设备")]
public enum CoilRegisterDeviceEnum
{
    /// <summary>
    /// 电动开关阀开信号
    /// </summary>
    [Description("电动开关阀开信号")]
    电动开关阀开信号 = 0x010101,

    /// <summary>
    /// 电动开关阀关信号
    /// </summary>
    [Description("电动开关阀关信号")]
    电动开关阀关信号 = 0x010102,

    /// <summary>
    /// 气动开关阀1电磁阀
    /// </summary>
    [Description("气动开关阀1电磁阀")]
    气动开关阀1电磁阀 = 0x010103,

    /// <summary>
    /// 气动开关阀2电磁阀
    /// </summary>
    [Description("气动开关阀2电磁阀")]
    气动开关阀2电磁阀 = 0x010104,

    /// <summary>
    /// 变频器启动信号
    /// </summary>
    [Description("变频器启动信号")]
    变频器启动信号 = 0x010206,

    /// <summary>
    /// 变频器故障应答信号
    /// </summary>
    [Description("变频器故障应答信号")]
    变频器故障应答信号 = 0x010207,

    /// <summary>
    /// 声光报警输出
    /// </summary>
    [Description("声光报警输出")]
    声光报警输出 = 0x010208,

}
