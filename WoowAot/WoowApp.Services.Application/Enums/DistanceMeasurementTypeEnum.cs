using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics.Metrics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 距离测量类型
/// </summary>
[ConstSelector("距离测量类型")]
public enum DistanceMeasurementTypeEnum
{
    /// <summary>
    /// 激光
    /// </summary>
    [Description("激光")]
    激光 = 10,

    /// <summary>
    /// 增量编码器
    /// </summary>
    [Description("增量编码器")]
    增量编码器 = 20,

    /// <summary>
    /// 高精度增量编码器
    /// </summary>
    [Description("高精度增量编码器")]
    高精度增量编码器 = 30,
}
