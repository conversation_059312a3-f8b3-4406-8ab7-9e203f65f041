using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 传感器大类
/// </summary>
[ConstSelector("传感器大类")]
public enum SensorTypeEnum
{
    /// <summary>
    /// 模拟量传感器
    /// </summary>
    [Description("模拟量传感器")]
    模拟量传感器 = 0,

    /// <summary>
    /// 高精度模拟量传感器
    /// </summary>
    [Description("高精度模拟量传感器")]
    高精度模拟量传感器 = 5,

    /// <summary>
    /// 数字量传感器
    /// </summary>
    [Description("数字量传感器")]
    数字量传感器 = 10,

    /// <summary>
    /// 开关量传感器
    /// </summary>
    [Description("开关量传感器")]
    开关量传感器 = 20,
}