using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 数字量控制方法
/// </summary>
[ConstSelector("数字量控制方法")]
public enum DigitalOutputControlMethodEnum
{

    /// <summary>
    /// 不控制
    /// </summary>
    [Description("不控制")]
    不控制 = 5,

    /// <summary>
    /// 单电磁阀NC
    /// </summary>
    [Description("单电磁阀NC")]
    单电磁阀NC = 10,


    /// <summary>
    /// 单电磁阀NO
    /// </summary>
    [Description("单电磁阀NO")]
    单电磁阀NO = 20,

    /// <summary>
    /// 双电磁阀NC
    /// </summary>
    [Description("双电磁阀NC")]
    双电磁阀NC = 30,

    /// <summary>
    /// 双电磁阀NO
    /// </summary>
    [Description("双电磁阀NO")]
    双电磁阀NO = 40,

}
