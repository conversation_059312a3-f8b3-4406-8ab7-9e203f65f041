using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 输入寄存器设备
/// </summary>
[ConstSelector("输入寄存器设备")]
public enum InputRegisterDeviceEnum
{
    /// <summary>
    /// 环境温度
    /// </summary>
    [Description("环境温度")]
    环境温度 = 0x040101,

    /// <summary>
    /// 电动开关阀阀位反馈
    /// </summary>
    [Description("电动开关阀阀位反馈")]
    电动开关阀阀位反馈 = 0x040301,

    /// <summary>
    /// 气动调节阀1阀位反馈
    /// </summary>
    [Description("气动调节阀1阀位反馈")]
    气动调节阀1阀位反馈 = 0x040302,

    /// <summary>
    /// 气动调节阀2阀位反馈
    /// </summary>
    [Description("气动调节阀2阀位反馈")]
    气动调节阀2阀位反馈 = 0x040303,

    /// <summary>
    /// 气动调节阀3阀位反馈
    /// </summary>
    [Description("气动调节阀3阀位反馈")]
    气动调节阀3阀位反馈 = 0x040304,

    /// <summary>
    /// 阀前压力
    /// </summary>
    [Description("阀前压力")]
    阀前压力 = 0x040404,

    /// <summary>
    /// 压差
    /// </summary>
    [Description("压差")]
    压差 = 0x040405,

    /// <summary>
    /// 流量计
    /// </summary>
    [Description("流量计")]
    流量计 = 0x040606,

    /// <summary>
    /// 液位计
    /// </summary>
    [Description("液位计")]
    液位计 = 0x040607,

    /// <summary>
    /// 变频器转速
    /// </summary>
    [Description("变频器转速")]
    变频器转速 = 0x040701,

    /// <summary>
    /// 变频器电流
    /// </summary>
    [Description("变频器电流")]
    变频器电流 = 0x040702,

}
