using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 日志动作枚举
/// </summary>
[ConstSelector("日志动作枚举")]
public enum LogDataActionEnum
{

    /// <summary>
    /// 失败
    /// </summary>
    [Description("失败")]
    失败 = -10,

    /// <summary>
    /// 故障等待
    /// </summary>
    [Description("故障等待")]
    故障等待 = -5,

    /// <summary>
    /// 开始
    /// </summary>
    [Description("开始")]
    开始 = 10,

    /// <summary>
    /// 数据日志
    /// </summary>
    [Description("数据日志")]
    数据日志 = 0,

    /// <summary>
    /// 信息
    /// </summary>
    [Description("信息")]
    信息 = 5,

    /// <summary>
    /// 完成
    /// </summary>
    [Description("完成")]
    完成 = 20,

}