using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WoowApp.Services.Application.Enums;

/// <summary>
/// 保持寄存器设备
/// </summary>
[ConstSelector("保持寄存器设备")]
public enum HoldingRegisterDeviceEnum
{
    /// <summary>
    /// 气动调节阀1控制信号
    /// </summary>
    [Description("气动调节阀1控制信号")]
    气动调节阀1控制信号 = 0x030101,

    /// <summary>
    /// 气动调节阀2控制信号
    /// </summary>
    [Description("气动调节阀2控制信号")]
    气动调节阀2控制信号 = 0x030102,

    /// <summary>
    /// 气动调节阀3控制信号
    /// </summary>
    [Description("气动调节阀3控制信号")]
    气动调节阀3控制信号 = 0x030103,

    /// <summary>
    /// 变频器调节信号
    /// </summary>
    [Description("变频器调节信号")]
    变频器调节信号 = 0x030104,

}
