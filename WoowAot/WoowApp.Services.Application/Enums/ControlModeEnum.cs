using System.ComponentModel;

namespace WoowApp.Services.Application.Enums;


/// <summary>
/// 控制模式
/// </summary>
[ConstSelector("控制模式")]
public enum ControlModeEnum
{
    /// <summary>
    /// 停止
    /// </summary>
    [Description("停止")]
    Off = 10,

    /// <summary>
    /// 自动稳定压力
    /// </summary>
    [Description("自动稳定压力")]
    AutoStabilizingPressure = 20,

    /// <summary>
    /// 自动稳定压差
    /// </summary>
    [Description("自动稳定压差")]
    AutoStablePressureDrop = 30,

    /// <summary>
    /// 手动
    /// </summary>
    [Description("手动")]
    Manual = 40,
}
