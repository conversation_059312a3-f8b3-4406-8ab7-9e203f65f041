global using WoowApp.Services.Shared.Ioc;
global using WoowApp.Services.Shared.DynamicApiController;
global using WoowApp.Services.Shared.OpenApi;
global using SqlSugar;
global using System.Linq;
global using System.ComponentModel.DataAnnotations;
global using WoowApp.Services.Core.Extensions;
global using MiniExcelLibs.Attributes;
global using WoowApp.Services.Core.Attributes;
global using WoowApp.Services.Core.Base;
global using Mapster;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Http;
global using WoowApp.Services.Application.Consts;
//global using WoowApp.Services.Application.Dto;
global using WoowApp.Services.Application.Entities;
global using WoowApp.Services.Application.Managers;
global using WoowApp.Services.Core.DatabaseAccessor;
global using WoowApp.Services.Core.Services;
global using WoowApp.Services.Core.Services.Base;
global using WoowApp.Services.Shared.FriendlyException;
global using WoowApp.Services.Core.Utils;
global using System.Text.Json.Serialization;
global using WoowApp.Services.Shared.Snowflake;
global using Swashbuckle.AspNetCore.Annotations;
global using WoowApp.Services.Core;
global using Microsoft.Extensions.DependencyInjection;
global using WoowApp.Services.Application.Models;
global using WoowApp.Services.Application.Enums;
global using WoowApp.Services.Application.Managers.Dto;
global using WoowApp.Services.Application.Service.Dto;
global using WoowApp.Services.Application.Utils;
global using WoowApp.Services.Core.Enums;
global using WoowApp.Services.Core.Managers;