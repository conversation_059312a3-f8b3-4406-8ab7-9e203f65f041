namespace WoowApp.Services.Application.Utils;

/// <summary>
/// 数模转换
/// </summary>
public class SensorAnalogConvert
{
    /// <summary>
    /// 数字量转模拟量
    /// </summary>
    /// <param name="digitalValue">数字量</param>
    /// <param name="digitalMin">数字量下限</param>
    /// <param name="digitalMax">数字量上限</param>
    /// <param name="analogMin">模拟量下限</param>
    /// <param name="analogMax">模拟量上限</param>
    /// <returns></returns>
    public static decimal ToAnalogValue(decimal digitalValue, decimal digitalMin, decimal digitalMax, decimal analogMin, decimal analogMax)
    {
        var scale = (analogMax - analogMin) / (digitalMax - digitalMin);
        return analogMin + (scale * (digitalValue - digitalMin));
    }

    /// <summary>
    /// 模拟量转数字量
    /// </summary>
    /// <param name="analogValue">模拟量</param>
    /// <param name="digitalMin">数字量下限</param>
    /// <param name="digitalMax">数字量上限</param>
    /// <param name="analogMin">模拟量下限</param>
    /// <param name="analogMax">模拟量上限</param>
    /// <returns></returns>
    public static decimal ToDigitalValue(decimal analogValue, decimal digitalMin, decimal digitalMax, decimal analogMin, decimal analogMax)
    {
        var scale = (digitalMax - digitalMin) / (analogMax - analogMin);
        return digitalMin + (scale * (analogValue - analogMin));
    }

}
