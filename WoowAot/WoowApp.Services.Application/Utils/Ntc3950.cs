namespace WoowApp.Services.Application.Utils;

/// <summary>
/// Ntc100K-B3950传感器Adc转摄氏度算法
/// </summary>
public class Ntc3950
{
    /// <summary>
    /// 串联电阻(单位欧姆)
    /// </summary>
    private const int NtcSeriesResistor = 10_000;
    /// <summary>
    /// 量程最低温度(摄氏度)
    /// </summary>
    private const decimal MinTemperature = -55;

    /// <summary>
    /// 量程最高温度(摄氏度)
    /// </summary>
    private static decimal MaxTemperature => MinTemperature + ResistanceList.Length - 1;

    #region 温度电阻对照表

    /// <summary>
    /// 温度电阻对照表（-55摄氏度至129摄氏度）
    /// </summary>
    private static readonly uint[] ResistanceList =
    [
        8989000,
        8242680,
        7592960,
        7021380,
        6513750, // -55,-54,-53,-52,-51
        6059060,
        5648680,
        5275800,
        4935020,
        4621990,
        4333220,
        4065840,
        3817520,
        3586310,
        3370600, // -50,-49,-48,-47,-46,-45,-44,-43,-42,-41
        3169000,
        2980330,
        2803600,
        2637910,
        2482470,
        2336580,
        2199620,
        2071020,
        1950230,
        1836790, // -40,-39,-38,-37,-36,-35,-34,-33,-32,-31
        1730230,
        1630150,
        1536140,
        1447840,
        1364900,
        1287000,
        1213820,
        1145090,
        1080530,
        1019890, // -30,-29,-28,-27,-26,-25,-24,-23,-22,-21
        962912,
        909379,
        859074,
        811797,
        767359,
        725581,
        686296,
        649348,
        614590,
        581883,  // -20,-19,-18,-17,-16,-15,-14,-13,-12,-11
        551100,
        522117,
        494824,
        469113,
        444886,
        422050,
        400518,
        380209,
        361048,
        342963,  // -10,-9,-8,-7,-6,-5,-4,-3,-2,-1
        326560,
        309764,
        294529,
        280131,
        266520,
        253647,
        241470,
        229946,
        219036,
        208706,  // 0,1, 2, 3,4,5,6,7,8,9
        198920,
        189647,
        180857,
        172523,
        164618,
        157118,
        150000,
        143243,
        136827,
        130731,  // 10,11 ,12, 13,14,15,16,17,18,19
        124940,
        119435,
        114202,
        109225,
        104491,
        100000,
        95699,
        91617,
        87731,
        84028,   // 20,21, 22, 23,24,25,26,27,28,29
        80501,
        77140,
        73936,
        70881,
        67968,
        65188,
        62537,
        60006,
        57590,
        55283,   // 30,31, 32, 33,34,35,36,37,38,39
        53080,
        50976,
        48965,
        47044,
        45207,
        43451,
        41771,
        40165,
        38628,
        37157,   // 40,41, 42, 43,34,35,36,37,38,39
        35750,
        34402,
        33112,
        31876,
        30692,
        29558,
        28471,
        27429,
        26430,
        25472,   // 50,51, 52, 53,54,55,56,57,58,59
        24554,
        23672,
        22827,
        22016,
        21237,
        20489,
        19771,
        19082,
        18420,
        17784,   // 60,61, 62, 63,64,65,66,67,68,69
        17172,
        16585,
        16020,
        15477,
        14955,
        14453,
        13970,
        13505,
        13058,
        12628,   // 70,71, 72, 73,74,75,76,77,78,79
        12213,
        11815,
        11431,
        11061,
        10705,
        10362,
        10031,
        9712,
        9405,
        9110,    // 80,81, 82, 83,84,85,86,87,88,89
        8824,
        8549,
        8284,
        8028,
        7782,
        7544,
        7314,
        7093,
        6879,
        6673,    // 90,91, 92, 93,94,95,96,97,98,99
        6474,
        6281,
        6096,
        5916,
        5743,
        5576,
        5415,
        5259,
        5108,
        4963,    // 100,101,102, 103,104,105,106,107,108,109
        4822,
        4687,
        4555,
        4428,
        4306,
        4187,
        4073,
        3962,
        3855,
        3751,    // 110,111,112, 113,114,115,116,117,118,119
        3651,
        3555,
        3461,
        3371,
        3283,
        3199,
        3100,
        3099,
        2899,
        2799,    // 120,121,122, 123,124,125,126,127,128,129
    ];

    #endregion 温度电阻对照表

    /// <summary>
    /// 查询表左索引（二分法查表）
    /// </summary>
    /// <param name="list">温度电阻对照表</param>
    /// <param name="currentResistance"></param>
    /// <returns></returns>
    private static int NtcLookup(uint[] list, int currentResistance)
    {
        int indexLeft = 0;
        int indexRight = list.Length - 1;
        if (currentResistance >= list[0]) return indexLeft;
        if (currentResistance <= list[^1]) return indexRight;

        while ((indexRight - indexLeft) > 1) //二分法查表
        {
            var middleIndex = (indexLeft + indexRight) >> 1;
            var middleValue = list[middleIndex];
            if (currentResistance == middleValue)
                return middleIndex;
            else if (currentResistance > middleValue)
                indexRight = middleIndex;
            else if (currentResistance < middleValue)
                indexLeft = middleIndex;
        }
        return indexLeft;
    }

    /// <summary>
    /// adc值转实际温度
    /// </summary>
    /// <param name="adc">采集电压</param>
    /// <returns></returns>
    public static decimal GetTemperature(ushort adc)
    {
        // 计算当前温度传感器电阻R2, 其中4096非电压值，而是硬件12位线性采样值 2^12 = 4096，未转换为电压，供电电压 U=3.3伏, R1为串联分压电阻
        // U / (R1 + R2) * R2 = U * (adc / 4096)，
        // 可推导出以下公式 R2 = （R1 * adc）/ (4096 - adc)
        var currentResistance = adc * NtcSeriesResistor / (4096 - adc);
        if (currentResistance >= ResistanceList[0]) // 下边界
        {
            return MinTemperature;  // 取量程最小值
        }
        if (currentResistance <= ResistanceList[^1])// 上边界
        {
            return MaxTemperature;//取量程最大值
        }
        // 查表求左索引
        var index = NtcLookup(ResistanceList, currentResistance);
        // 计算度间阻差
        var differenceValue = ResistanceList[index] - ResistanceList[index + 1];
        // 计算小数位
        var decimalNumber = (decimal)(ResistanceList[index] - currentResistance) / differenceValue;
        // 小数点保留两位
        var result = MinTemperature + index + decimal.Round(decimalNumber, 2);
        return result;
    }

}
