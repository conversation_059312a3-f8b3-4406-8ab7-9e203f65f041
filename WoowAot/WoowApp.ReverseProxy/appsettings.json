{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ReverseProxy": {"Routes": {"apiRoute": {"Match": {"Path": "/api/{**catch-all}"}, "Transforms": [{"PathRemovePrefix": "/api"}], "ClusterId": "apiCluster"}, "rootRoute": {"Match": {"Path": "/{**catch-all}"}, "Transforms": [], "ClusterId": "rootCluster"}}, "Clusters": {"apiCluster": {"Destinations": {"destination1": {"Address": "https://localhost:48090"}}}, "rootCluster": {"Destinations": {"destination1": {"Address": "http://**************:18090"}}}}}}